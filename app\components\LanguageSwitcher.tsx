"use client";

import { useLanguage } from "../context/LanguageContext";

export default function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage();

  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = e.target.value as "sv" | "en";
    console.log("Switching language to:", newLanguage); // Debug log
    setLanguage(newLanguage);
  };

  return (
    <select
      value={language}
      onChange={handleLanguageChange}
      className="ml-4 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
    >
      <option value="sv">Svenska</option>
      <option value="en">English</option>
    </select>
  );
}
