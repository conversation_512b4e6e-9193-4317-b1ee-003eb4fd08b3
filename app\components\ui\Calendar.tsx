"use client";

import { useState } from "react";

interface CalendarProps {
  selectedDate?: Date;
  onSelect?: (date: Date) => void;
}

export function Calendar({ selectedDate, onSelect }: CalendarProps) {
  const [currentDate] = useState(selectedDate || new Date());

  // Basic calendar implementation
  return (
    <div className="p-4 bg-white rounded-lg shadow">
      {/* Calendar implementation here */}
      <div>{currentDate.toLocaleDateString()}</div>
    </div>
  );
}
