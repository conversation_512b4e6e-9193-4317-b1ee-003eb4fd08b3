import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";

export interface UserPreferences {
  itemsPerPage: number;
}

const DEFAULT_PREFERENCES: UserPreferences = {
  itemsPerPage: 10,
};

export function useUserPreferences() {
  const { data: session, status } = useSession();
  const [preferences, setPreferences] =
    useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdatedPreference, setLastUpdatedPreference] = useState<
    string | null
  >(null);

  // Load preferences when user is authenticated
  useEffect(() => {
    const loadPreferences = async () => {
      if (status === "authenticated" && session?.user) {
        try {
          setIsLoading(true);
          setError(null);

          const response = await fetch("/api/user/preferences");

          if (!response.ok) {
            throw new Error("Failed to load preferences");
          }

          const data = await response.json();
          setPreferences(data.preferences || DEFAULT_PREFERENCES);
        } catch (err) {
          console.error("Error loading preferences:", err);
          setError((err as Error).message);
          // Keep default preferences on error
          setPreferences(DEFAULT_PREFERENCES);
        } finally {
          setIsLoading(false);
        }
      } else if (status === "unauthenticated") {
        // Use default preferences for unauthenticated users
        setPreferences(DEFAULT_PREFERENCES);
        setIsLoading(false);
      }
    };

    loadPreferences();
  }, [session, status]);

  // Update preferences
  const updatePreferences = async (
    newPreferences: Partial<UserPreferences>
  ) => {
    if (status !== "authenticated" || !session?.user) {
      console.warn("Cannot update preferences: user not authenticated");
      return false;
    }

    try {
      setError(null);

      const response = await fetch("/api/user/preferences", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ preferences: newPreferences }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update preferences");
      }

      const data = await response.json();
      setPreferences(data.preferences);
      return true;
    } catch (err) {
      console.error("Error updating preferences:", err);
      setError((err as Error).message);
      return false;
    }
  };

  // Update specific preference with optimistic update
  const updateItemsPerPage = useCallback(
    async (itemsPerPage: number) => {
      console.log(
        "useUserPreferences: updateItemsPerPage called with:",
        itemsPerPage
      );
      console.log("useUserPreferences: Current preferences:", preferences);
      console.log("useUserPreferences: Auth status:", status);

      if (status !== "authenticated" || !session?.user) {
        console.warn("Cannot update preferences: user not authenticated");
        return false;
      }

      // Optimistic update - update local state immediately
      const previousPreferences = preferences;
      console.log("useUserPreferences: Applying optimistic update");
      setPreferences((prev) => ({ ...prev, itemsPerPage }));
      setLastUpdatedPreference("itemsPerPage");

      try {
        console.log("useUserPreferences: Calling updatePreferences API");
        const success = await updatePreferences({ itemsPerPage });
        console.log("useUserPreferences: API call result:", success);

        if (!success) {
          console.log(
            "useUserPreferences: API call failed, reverting optimistic update"
          );
          // Revert on failure
          setPreferences(previousPreferences);
          setLastUpdatedPreference(null);
        } else {
          console.log(
            "useUserPreferences: API call successful, keeping optimistic update"
          );
        }
        return success;
      } catch (error) {
        console.error(
          "useUserPreferences: Error in updateItemsPerPage:",
          error
        );
        // Revert on error
        setPreferences(previousPreferences);
        setLastUpdatedPreference(null);
        throw error;
      }
    },
    [status, session, preferences, updatePreferences]
  );

  return {
    preferences,
    isLoading,
    error,
    updatePreferences,
    updateItemsPerPage,
    lastUpdatedPreference,
  };
}
