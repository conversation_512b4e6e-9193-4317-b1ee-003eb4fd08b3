"use client";

import { useLanguage } from "../context/LanguageContext";

export default function Statistics() {
  const { t, language } = useLanguage();

  console.log("Current language:", language); // Debug log
  console.log("Title translation:", t("statistics.title")); // Debug log

  return (
    <div>
      <h1>{t("statistics.title")}</h1>

      <div className="grid grid-cols-4 gap-4">
        <div>
          <h3>{t("statistics.metrics.totalProducts")}</h3>
          <p>127</p>
        </div>
        <div>
          <h3>{t("statistics.metrics.totalStock")}</h3>
          <p>220</p>
        </div>
        <div>
          <h3>{t("statistics.metrics.lowStock")}</h3>
          <p>100</p>
        </div>
        <div>
          <h3>{t("statistics.metrics.outOfStock")}</h3>
          <p>8</p>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mt-8">
        <div>
          <h2>{t("statistics.productTypes")}</h2>
          {/* Product types content */}
        </div>
        <div>
          <h2>{t("statistics.financialOverview")}</h2>
          <div>
            <p>{t("statistics.metrics.averagePrice")}</p>
            <p>{t("common.currency")} 403.52</p>
          </div>
          <div>
            <p>{t("statistics.metrics.totalStockValue")}</p>
            <p>{t("common.currency")} 105660.00</p>
          </div>
        </div>
      </div>

      <div className="mt-8">
        <h2>{t("statistics.topProducts")}</h2>
        {/* Top products list */}
      </div>
    </div>
  );
}
