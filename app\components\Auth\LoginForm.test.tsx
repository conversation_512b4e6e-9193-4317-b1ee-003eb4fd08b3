import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import LoginForm from './LoginForm'; // Adjust path as necessary
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  signIn: jest.fn(),
  useSession: jest.fn(() => ({ data: null, status: 'unauthenticated' })), // Default mock for useSession if LoginForm uses it
}));

// Mock next/navigation
const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: mockRouterPush,
    prefetch: jest.fn(), // Add prefetch if used by component
  })),
}));

describe('LoginForm', () => {
  beforeEach(() => {
    // Clear mock call history before each test
    (signIn as jest.Mock).mockClear();
    mockRouterPush.mockClear();
  });

  it('renders the login form correctly', () => {
    render(<LoginForm />);
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
  });

  it('handles successful login', async () => {
    (signIn as jest.Mock).mockResolvedValue({ ok: true, error: null, url: '/dashboard' });
    render(<LoginForm />);

    fireEvent.change(screen.getByLabelText(/email/i), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: 'password123' } });
    fireEvent.click(screen.getByRole('button', { name: /login/i }));

    await waitFor(() => {
      expect(signIn).toHaveBeenCalledWith('credentials', {
        redirect: false,
        email: '<EMAIL>',
        password: 'password123',
      });
    });
    
    await waitFor(() => {
      expect(mockRouterPush).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('handles failed login and displays error message', async () => {
    const errorMessage = 'Invalid credentials';
    (signIn as jest.Mock).mockResolvedValue({ ok: false, error: errorMessage, url: null });
    render(<LoginForm />);

    fireEvent.change(screen.getByLabelText(/email/i), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: 'wrongpassword' } });
    fireEvent.click(screen.getByRole('button', { name: /login/i }));

    await waitFor(() => {
      expect(signIn).toHaveBeenCalledTimes(1);
    });
    
    expect(await screen.findByText(errorMessage)).toBeInTheDocument();
    expect(mockRouterPush).not.toHaveBeenCalled();
  });

  it('shows loading state when submitting', async () => {
    (signIn as jest.Mock).mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ ok: true, error: null }), 100)));
    render(<LoginForm />);

    fireEvent.change(screen.getByLabelText(/email/i), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: 'password123' } });
    fireEvent.click(screen.getByRole('button', { name: /login/i }));

    expect(screen.getByRole('button', { name: /logging in.../i })).toBeDisabled();
    await waitFor(() => expect(signIn).toHaveBeenCalled());
  });
  
  // Example of client-side validation test (if LoginForm had it)
  // For instance, if the button was disabled until fields are filled.
  // The current LoginForm relies on HTML 'required' attribute, which JSDOM doesn't fully enforce behaviorally for form submission blocking.
  // If there were custom logic:
  /*
  it('disables submit button if email or password are empty', () => {
    render(<LoginForm />);
    const loginButton = screen.getByRole('button', { name: /login/i });
    
    // Initially button might be enabled or disabled based on implementation
    // fireEvent.change(screen.getByLabelText(/email/i), { target: { value: '' } });
    // expect(loginButton).toBeDisabled(); // Assuming custom validation logic
  });
  */
});
