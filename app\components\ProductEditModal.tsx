"use client";

import { Product } from "../types";
import { useState } from "react";
import { useLanguage } from "../context/LanguageContext";
import { updateProduct } from "../utils/api-client";

interface ProductEditModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

export default function ProductEditModal({
  product,
  isOpen,
  onClose,
  onUpdate,
}: ProductEditModalProps) {
  const { t } = useLanguage();
  const [updating, setUpdating] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [formData, setFormData] = useState({
    name: product.name,
    sku: product.sku || "",
    regular_price: product.regular_price || "",
    sale_price: product.sale_price || "",
    description: product.description || "",
    stock_quantity: product.stock_quantity || 0,
    manage_stock: product.manage_stock || false,
    stock_status: product.stock_status || "instock",
  });

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setUpdating(true);
      await updateProduct(product.id, formData);
      onUpdate();
      onClose();
    } catch (error) {
      console.error("Failed to update product:", error);
      alert(t("products.errors.updateFailed"));
    } finally {
      setUpdating(false);
    }
  };

  // Replace this with the actual preview component
  const PreviewHTML = ({ html }: { html: string }) => (
    <div
      className="prose max-w-none"
      dangerouslySetInnerHTML={{ __html: html }}
    />
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold mb-4">
          {t("products.edit.title")} - {product.name}
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              {t("products.fields.name")}
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              {t("products.fields.sku")}
            </label>
            <input
              type="text"
              value={formData.sku}
              onChange={(e) =>
                setFormData({ ...formData, sku: e.target.value })
              }
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                {t("products.fields.regularPrice")}
              </label>
              <input
                type="number"
                value={formData.regular_price}
                onChange={(e) =>
                  setFormData({ ...formData, regular_price: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                {t("products.fields.salePrice")}
              </label>
              <input
                type="number"
                value={formData.sale_price}
                onChange={(e) =>
                  setFormData({ ...formData, sale_price: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                step="0.01"
              />
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">
                {t("products.fields.description")}
              </label>
              <button
                type="button"
                onClick={() => setShowPreview(!showPreview)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                {showPreview ? "Show Editor" : "Show Preview"}
              </button>
            </div>

            {showPreview ? (
              <div className="border rounded-md p-4 bg-gray-50">
                <PreviewHTML html={formData.description} />
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex flex-wrap gap-2 mb-2">
                  <button
                    type="button"
                    onClick={() => {
                      const textarea = document.getElementById(
                        "description"
                      ) as HTMLTextAreaElement;
                      const start = textarea.selectionStart;
                      const end = textarea.selectionEnd;
                      const text = textarea.value;
                      const before = text.substring(0, start);
                      const selection = text.substring(start, end);
                      const after = text.substring(end);
                      const newText = `${before}<strong>${selection}</strong>${after}`;
                      setFormData({ ...formData, description: newText });
                    }}
                    className="px-2 py-1 text-sm border rounded hover:bg-gray-100"
                  >
                    B
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      const textarea = document.getElementById(
                        "description"
                      ) as HTMLTextAreaElement;
                      const start = textarea.selectionStart;
                      const end = textarea.selectionEnd;
                      const text = textarea.value;
                      const before = text.substring(0, start);
                      const selection = text.substring(start, end);
                      const after = text.substring(end);
                      const newText = `${before}<em>${selection}</em>${after}`;
                      setFormData({ ...formData, description: newText });
                    }}
                    className="px-2 py-1 text-sm border rounded hover:bg-gray-100 italic"
                  >
                    I
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      const textarea = document.getElementById(
                        "description"
                      ) as HTMLTextAreaElement;
                      const start = textarea.selectionStart;
                      const end = textarea.selectionEnd;
                      const text = textarea.value;
                      const before = text.substring(0, start);
                      const selection = text.substring(start, end);
                      const after = text.substring(end);
                      const newText = `${before}<ul>\n<li>${selection}</li>\n</ul>${after}`;
                      setFormData({ ...formData, description: newText });
                    }}
                    className="px-2 py-1 text-sm border rounded hover:bg-gray-100"
                  >
                    List
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      const textarea = document.getElementById(
                        "description"
                      ) as HTMLTextAreaElement;
                      const start = textarea.selectionStart;
                      const end = textarea.selectionEnd;
                      const text = textarea.value;
                      const before = text.substring(0, start);
                      const selection = text.substring(start, end);
                      const after = text.substring(end);
                      const newText = `${before}<p>${selection}</p>${after}`;
                      setFormData({ ...formData, description: newText });
                    }}
                    className="px-2 py-1 text-sm border rounded hover:bg-gray-100"
                  >
                    P
                  </button>
                </div>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  rows={8}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono text-sm"
                />
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                {t("products.fields.stockQuantity")}
              </label>
              <input
                type="number"
                value={formData.stock_quantity}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    stock_quantity: parseInt(e.target.value),
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                {t("products.fields.stockStatus")}
              </label>
              <select
                value={formData.stock_status}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    stock_status: e.target.value as
                      | "instock"
                      | "outofstock"
                      | "onbackorder",
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="instock">{t("products.stock.inStock")}</option>
                <option value="outofstock">
                  {t("products.stock.outOfStock")}
                </option>
                <option value="onbackorder">
                  {t("products.stock.onBackorder")}
                </option>
              </select>
            </div>
          </div>

          <div className="flex items-center mt-4">
            <input
              type="checkbox"
              checked={formData.manage_stock}
              onChange={(e) =>
                setFormData({ ...formData, manage_stock: e.target.checked })
              }
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label className="ml-2 text-sm text-gray-700">
              {t("products.fields.manageStock")}
            </label>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t("common.cancel")}
            </button>
            <button
              type="submit"
              disabled={updating}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {updating ? t("common.saving") : t("common.save")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
