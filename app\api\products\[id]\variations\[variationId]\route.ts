import { NextResponse } from "next/server";

export async function PUT(
  request: Request,
  { params }: { params: { id: string; variationId: string } }
) {
  try {
    if (
      !process.env.WC_CONSUMER_KEY ||
      !process.env.WC_CONSUMER_SECRET ||
      !process.env.NEXT_PUBLIC_WC_API_URL
    ) {
      return NextResponse.json(
        { error: "WooCommerce API credentials not configured" },
        { status: 500 }
      );
    }

    const data = await request.json();
    const auth = Buffer.from(
      `${process.env.WC_CONSUMER_KEY}:${process.env.WC_CONSUMER_SECRET}`
    ).toString("base64");

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WC_API_URL}/products/${params.id}/variations/${params.variationId}`,
      {
        method: "PUT",
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("WooCommerce API Error:", errorText);
      return NextResponse.json(
        { error: `WooCommerce API error: ${response.statusText}` },
        { status: response.status }
      );
    }

    const updatedVariation = await response.json();
    return NextResponse.json(updatedVariation);
  } catch (error) {
    console.error("Variation Update API Error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
