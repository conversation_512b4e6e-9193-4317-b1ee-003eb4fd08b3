"use client";

import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSearchParams, useRouter } from "next/navigation";
import FilterBar from "../components/FilterBar";
import Pagination from "../components/Pagination";
import ProductTable from "../components/ProductTable";
import ItemsPerPageSelector from "../components/ItemsPerPageSelector";
import { Product, ProductFilter, ProductsResponse } from "@/types/index";
import { fetchProducts } from "../utils/api-client";
import { useLanguage } from "../context/LanguageContext";
import { useUserPreferences } from "../hooks/useUserPreferences";

export default function DashboardPage() {
  const { t } = useLanguage();
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const {
    preferences,
    isLoading: preferencesLoading,
    lastUpdatedPreference,
  } = useUserPreferences();
  const [isUpdatingItemsPerPage, setIsUpdatingItemsPerPage] = useState(false);
  const [queryKey, setQueryKey] = useState(0); // Force query refresh

  // Get current page from URL or default to 1
  const currentPage = Number(searchParams.get("page")) || 1;

  // Use user preference for items per page
  const itemsPerPage = preferences.itemsPerPage;

  // Initialize filters from URL parameters
  const [filters, setFilters] = useState<ProductFilter>({
    search: searchParams.get("search") || "",
    category: searchParams.get("category") || "",
    stockStatus:
      (searchParams.get("stock_status") as ProductFilter["stockStatus"]) ||
      "all",
  });

  const {
    data: productsData,
    isLoading,
    error,
    isFetching,
    refetch,
  } = useQuery<ProductsResponse, Error>({
    queryKey: [
      "products",
      currentPage,
      filters.search,
      filters.stockStatus,
      filters.category,
      itemsPerPage, // Include itemsPerPage in query key
      queryKey, // Force refresh key
    ],
    queryFn: () => {
      console.log("Dashboard: Executing fetchProducts with params:", {
        search: filters.search,
        page: currentPage,
        per_page: itemsPerPage,
        stockStatus: filters.stockStatus,
        category: filters.category,
      });
      return fetchProducts({
        search: filters.search,
        page: currentPage,
        per_page: itemsPerPage,
        stockStatus: filters.stockStatus,
        category: filters.category,
      });
    },
    enabled: !preferencesLoading, // Don't fetch until preferences are loaded
  });

  const products = productsData?.data ?? [];
  const totalItems = productsData?.total ?? 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Watch for preference changes and force refresh
  useEffect(() => {
    console.log(
      "Dashboard: lastUpdatedPreference changed to:",
      lastUpdatedPreference
    );
    if (lastUpdatedPreference === "itemsPerPage") {
      console.log(
        "Dashboard: Forcing query refresh due to itemsPerPage change"
      );
      setQueryKey((prev) => prev + 1);
    }
  }, [lastUpdatedPreference]);

  // Handle immediate update when items per page changes
  const handleImmediateUpdate = async (newItemsPerPage: number) => {
    console.log("handleImmediateUpdate called with:", newItemsPerPage);
    console.log("Current itemsPerPage in component:", itemsPerPage);
    setIsUpdatingItemsPerPage(true);

    try {
      // Reset to page 1 when changing items per page to avoid empty pages
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.set("page", "1");

      // Update URL without navigation to reset page to 1
      window.history.replaceState(
        null,
        "",
        `/dashboard?${newSearchParams.toString()}`
      );

      // Manually fetch with the new itemsPerPage value to ensure immediate update
      console.log(
        "Manually fetching products with new itemsPerPage:",
        newItemsPerPage
      );
      console.log("About to call fetchProducts with filters:", filters);

      try {
        console.log("Calling fetchProducts now...");
        const newProductsData = await fetchProducts({
          search: filters.search,
          page: 1, // Reset to page 1
          per_page: newItemsPerPage, // Use the new value directly
          stockStatus: filters.stockStatus,
          category: filters.category,
        });
        console.log("fetchProducts call completed");

        console.log(
          "Manual fetch completed, updating cache with data:",
          newProductsData
        );

        // Update the query cache with the new data
        queryClient.setQueryData(
          [
            "products",
            1, // page 1
            filters.search,
            filters.stockStatus,
            filters.category,
            newItemsPerPage, // new itemsPerPage
            queryKey + 1, // incremented queryKey
          ],
          newProductsData
        );

        console.log("Cache updated successfully");
      } catch (fetchError) {
        console.error("Manual fetch failed:", fetchError);
        throw fetchError; // Re-throw to trigger fallback
      }

      // Force query refresh by incrementing the key
      setQueryKey((prev) => prev + 1);

      console.log("handleImmediateUpdate completed with manual fetch");
    } catch (error) {
      console.error("Error in handleImmediateUpdate:", error);
      // Fallback to invalidation if manual fetch fails
      await queryClient.invalidateQueries({
        queryKey: ["products"],
      });
    } finally {
      setIsUpdatingItemsPerPage(false);
    }
  };

  // Show loading state while preferences are loading
  if (preferencesLoading) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">{t("dashboard.title")}</h1>
        <ItemsPerPageSelector
          onImmediateUpdate={handleImmediateUpdate}
          className="ml-auto"
        />
      </div>

      <FilterBar filters={filters} onFilterChange={setFilters} />

      {isUpdatingItemsPerPage && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          Updating items per page...
        </div>
      )}

      <div className="space-y-4">
        <div className="bg-white rounded-lg shadow">
          {(isLoading || isFetching) && (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2">{t("dashboard.loading")}</p>
            </div>
          )}

          {!isLoading && !isFetching && error && (
            <div className="p-6 text-center text-red-600 bg-red-50">
              <p>
                {t("dashboard.error")}: {error.message}
              </p>
              <button
                onClick={() => refetch()}
                className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                {t("dashboard.retry")}
              </button>
            </div>
          )}

          {!isLoading && !isFetching && !error && products.length === 0 && (
            <div className="p-6 text-center text-gray-500">
              <p>{t("dashboard.noProducts")}</p>
            </div>
          )}

          {!isLoading && !error && products.length > 0 && (
            <ProductTable
              products={products}
              onProductUpdate={() => refetch()}
            />
          )}
        </div>

        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            isLoading={isLoading || isFetching}
          />
        )}
      </div>
    </div>
  );
}
