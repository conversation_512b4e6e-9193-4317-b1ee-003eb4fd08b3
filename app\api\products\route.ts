import { NextResponse } from "next/server";
import { Product, ProductVariation } from "../../types";

export const dynamic = "force-dynamic";

const WC_API_URL = process.env.NEXT_PUBLIC_WC_API_URL;
const WC_CONSUMER_KEY = process.env.WC_CONSUMER_KEY;
const WC_CONSUMER_SECRET = process.env.WC_CONSUMER_SECRET;
const MAX_WC_PER_PAGE = 100;
const VARIATION_FETCH_CONCURRENCY = 10;

// Fetch variations for a single product
async function fetchVariations(
  productId: number,
  auth: string
): Promise<ProductVariation[] | null> {
  const url = `${WC_API_URL}/products/${productId}/variations?per_page=${MAX_WC_PER_PAGE}`;
  try {
    const response = await fetch(url, {
      headers: {
        Authorization: `Basic ${auth}`,
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });
    if (!response.ok) {
      let errorText = response.statusText;
      try {
        errorText = await response.text();
      } catch {}
      console.error(
        `Failed to fetch variations for product ${productId}: ${response.status} ${errorText}`
      );
      return null;
    }
    const data = await response.json();
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error(`Error fetching variations for product ${productId}:`, error);
    return null;
  }
}

// Fetch all products matching filters with pagination
async function fetchAllProducts(
  params: URLSearchParams,
  auth: string
): Promise<Product[]> {
  let allProducts: Product[] = [];
  let currentPage = 1;
  let totalPages = 1;

  params.set("per_page", MAX_WC_PER_PAGE.toString());

  do {
    params.set("page", currentPage.toString());
    const url = `${WC_API_URL}/products?${params.toString()}`;
    console.log(`Fetching WC page ${currentPage}/${totalPages || "?"}`);

    try {
      const response = await fetch(url, {
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      });
      if (!response.ok) {
        let errorText = response.statusText;
        try {
          errorText = await response.text();
        } catch {}
        console.error(
          `WooCommerce API error on page ${currentPage}: ${response.status} ${errorText}`
        );
        break;
      }
      if (currentPage === 1) {
        const totalPagesHeader = response.headers.get("X-WP-TotalPages");
        totalPages = totalPagesHeader ? Number(totalPagesHeader) : 1;
        console.log(`Total WC pages reported: ${totalPages}`);
      }
      const products: Product[] = await response.json();
      if (!Array.isArray(products)) {
        console.error(
          `Invalid data received from WC API on page ${currentPage}.`
        );
        break;
      }
      allProducts = allProducts.concat(products);
      if (products.length === 0 && currentPage <= totalPages) {
        console.warn(
          `Received 0 products on page ${currentPage}. Stopping fetch.`
        );
        break;
      }
      currentPage++;
    } catch (error) {
      console.error(`Error fetching WC page ${currentPage}:`, error);
      break;
    }
  } while (currentPage <= totalPages);

  console.log(`Fetched a total of ${allProducts.length} products from WC.`);
  return allProducts;
}

// Fetch variations with concurrency limit
async function fetchVariationsWithLimit(
  productIds: number[],
  auth: string,
  limit: number
): Promise<Map<number, ProductVariation[] | null>> {
  const results = new Map<number, ProductVariation[] | null>();
  const queue = [...productIds];

  async function worker() {
    while (queue.length > 0) {
      const productId = queue.shift();
      if (productId === undefined) break;
      const variations = await fetchVariations(productId, auth);
      results.set(productId, variations);
    }
  }

  const workers = Array(limit)
    .fill(null)
    .map(() => worker());
  await Promise.all(workers);

  return results;
}

export async function GET(request: Request) {
  const url = new URL(request.url);
  const searchParams = url.searchParams;

  const requestedPage = Number(searchParams.get("page")) || 1;
  const requestedPerPage = Number(searchParams.get("per_page")) || 10;
  const search = searchParams.get("search");
  const min_price = searchParams.get("min_price");
  const max_price = searchParams.get("max_price");
  const category = searchParams.get("category");
  const stock_status_filter = searchParams.get("stock_status") as
    | Product["stock_status"]
    | "all"
    | null;

  if (!WC_CONSUMER_KEY || !WC_CONSUMER_SECRET || !WC_API_URL) {
    console.error("Missing WooCommerce API credentials");
    return NextResponse.json(
      { error: "WooCommerce API credentials not configured" },
      { status: 500 }
    );
  }
  const auth = Buffer.from(`${WC_CONSUMER_KEY}:${WC_CONSUMER_SECRET}`).toString(
    "base64"
  );

  const needsVariationFiltering =
    stock_status_filter && stock_status_filter !== "all";

  try {
    let finalProducts: Product[] = [];
    let totalFilteredItems = 0;

    const baseWcApiParams = new URLSearchParams();
    if (search) baseWcApiParams.set("search", search);
    if (min_price) baseWcApiParams.set("min_price", min_price);
    if (max_price) baseWcApiParams.set("max_price", max_price);
    if (category) baseWcApiParams.set("category", category);

    if (!needsVariationFiltering) {
      baseWcApiParams.set("page", requestedPage.toString());
      baseWcApiParams.set("per_page", requestedPerPage.toString());

      const apiUrl = `${WC_API_URL}/products?${baseWcApiParams.toString()}`;
      const response = await fetch(apiUrl, {
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      });

      if (!response.ok)
        throw new Error(`WooCommerce API error: ${response.statusText}`);
      const productsData = await response.json();
      if (!Array.isArray(productsData))
        throw new Error("Invalid product data received");
      finalProducts = productsData;
      totalFilteredItems = Number(response.headers.get("X-WP-Total")) || 0;
    } else {
      const allMatchingProducts = await fetchAllProducts(baseWcApiParams, auth);

      const variableProductIds = allMatchingProducts
        .filter((p) => p.type === "variable")
        .map((p) => p.id);
      const variationsMap = await fetchVariationsWithLimit(
        variableProductIds,
        auth,
        VARIATION_FETCH_CONCURRENCY
      );

      const fullyFilteredProducts = allMatchingProducts.filter((product) => {
        if (
          product.type === "simple" ||
          product.type === "external" ||
          product.type === "grouped"
        ) {
          return product.stock_status === stock_status_filter;
        } else if (product.type === "variable") {
          const variations = variationsMap.get(product.id);
          if (!variations || variations.length === 0) return false;

          if (stock_status_filter === "instock") {
            return variations.some(
              (v) =>
                v.stock_status === "instock" || v.stock_status === "onbackorder"
            );
          } else if (stock_status_filter === "outofstock") {
            return variations.some((v) => v.stock_status === "outofstock");
          } else if (stock_status_filter === "onbackorder") {
            const hasOnBackorder = variations.some(
              (v) => v.stock_status === "onbackorder"
            );
            const hasInStock = variations.some(
              (v) => v.stock_status === "instock"
            );
            return hasOnBackorder && !hasInStock;
          }
        }
        return false;
      });

      totalFilteredItems = fullyFilteredProducts.length;
      const startIndex = (requestedPage - 1) * requestedPerPage;
      const endIndex = startIndex + requestedPerPage;
      finalProducts = fullyFilteredProducts.slice(startIndex, endIndex);
    }

    if (!Array.isArray(finalProducts)) {
      throw new Error(
        "Internal server error: processed product list is invalid."
      );
    }

    return NextResponse.json({
      data: finalProducts,
      total: totalFilteredItems,
    });
  } catch (error) {
    console.error("Error in GET /api/products:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to fetch products",
      },
      { status: 500 }
    );
  }
}
