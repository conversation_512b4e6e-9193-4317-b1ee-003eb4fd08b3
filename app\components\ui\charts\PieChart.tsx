"use client";

import { Pie } from "react-chartjs-2";
import { Chart as ChartJ<PERSON>, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js";

ChartJS.register(Arc<PERSON>lement, Toolt<PERSON>, Legend);

interface PieChartProps {
  data: Record<string, number>;
  title: string;
}

export function PieChart({ data, title }: PieChartProps) {
  const chartData = {
    labels: Object.keys(data),
    datasets: [
      {
        data: Object.values(data),
        backgroundColor: [
          "#FF6384",
          "#36A2EB",
          "#FFCE56",
          "#4BC0C0",
          "#9966FF",
          "#FF9F40",
        ],
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "right" as const,
      },
      title: {
        display: true,
        text: title,
      },
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <Pie data={chartData} options={options} />
    </div>
  );
}
