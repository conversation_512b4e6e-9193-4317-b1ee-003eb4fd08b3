import { NextResponse } from "next/server";
import { fetchWooCommerceData } from "./utils";

// Add cache for API responses
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const responseCache = new Map<string, { data: any; timestamp: number }>();

export const dynamic = "force-dynamic";

async function safeWooCommerceRequest<T>(
  endpoint: string,
  params = {}
): Promise<T | null> {
  try {
    return await fetchWooCommerceData<T>(endpoint, params);
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error);
    return null;
  }
}

interface Order {
  id: number;
  date_created: string;
  total: string;
  customer_id: number;
  line_items: {
    name: string;
    quantity: number;
    total: string;
    product_id: number;
  }[];
}

interface WooCommerceProduct {
  id: number;
  name: string;
  type: string;
  parent_id: number;
  variations?: number[];
  categories: {
    id: number;
    name: string;
    slug: string;
  }[];
  attributes: {
    id: number;
    name: string;
    option: string;
  }[];
}

async function fetchAllOrders(after: string, before: string): Promise<Order[]> {
  const pageSize = 100; // Maximum allowed by WooCommerce
  const maxPages = 50; // Maximum number of pages to fetch (5000 orders)
  let allOrders: Order[] = [];
  let currentPage = 1;
  let hasMorePages = true;

  while (hasMorePages && currentPage <= maxPages) {
    try {
      const response = await safeWooCommerceRequest<Order[]>("orders", {
        after,
        before,
        per_page: pageSize.toString(),
        page: currentPage.toString(),
      });

      if (!response || response.length === 0) {
        break;
      }

      allOrders = [...allOrders, ...response];

      if (response.length < pageSize) {
        hasMorePages = false;
      } else {
        currentPage++;
      }

      await new Promise((resolve) => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`Error fetching orders page ${currentPage}:`, error);
      break;
    }
  }

  if (currentPage >= maxPages) {
    console.warn(
      `Reached maximum page limit of ${maxPages}. Some orders may be missing.`
    );
  }

  return allOrders;
}

export async function POST(request: Request) {
  try {
    const { start, end } = await request.json();

    // Generate cache key
    const cacheKey = `stats-${start}-${end}`;

    // Check cache
    const cached = responseCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return NextResponse.json(cached.data);
    }

    // Calculate previous period dates
    const startDate = new Date(start);
    const endDate = new Date(end);
    const daysDiff =
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    const previousStart = new Date(
      startDate.getTime() - daysDiff * 24 * 60 * 60 * 1000
    ).toISOString();
    const previousEnd = new Date(
      endDate.getTime() - daysDiff * 24 * 60 * 60 * 1000
    ).toISOString();

    // Fetch all data in parallel using the new fetchAllOrders function
    const [currentOrders, previousOrders, topProducts, worstProducts] =
      await Promise.all([
        fetchAllOrders(start, end),
        fetchAllOrders(previousStart, previousEnd),
        fetchTopProducts(start, end),
        fetchWorstProducts(start, end),
      ]);

    if (!currentOrders || !previousOrders) {
      return NextResponse.json(
        { error: "Failed to fetch orders data" },
        { status: 500 }
      );
    }

    // Calculate statistics
    const currentStats = calculatePeriodStats(currentOrders);
    const previousStats = calculatePeriodStats(previousOrders);

    // Generate timeline data
    const [orderTimeline, revenueTimeline] = await Promise.all([
      generateTimeline(
        currentOrders,
        previousOrders,
        start,
        end,
        previousStart,
        previousEnd
      ),
      generateRevenueTimeline(
        currentOrders,
        previousOrders,
        start,
        end,
        previousStart,
        previousEnd
      ),
    ]);

    const response = {
      topStats: {
        totalRevenue: {
          value: `${currentStats.totalRevenue.toLocaleString("sv-SE")} kr`,
          ...calculateTrend(
            currentStats.totalRevenue,
            previousStats.totalRevenue
          ),
        },
        averageOrderValue: {
          value: `${currentStats.averageOrderValue.toLocaleString("sv-SE")} kr`,
          ...calculateTrend(
            currentStats.averageOrderValue,
            previousStats.averageOrderValue
          ),
        },
        totalOrders: {
          value: currentStats.totalOrders.toString(),
          ...calculateTrend(
            currentStats.totalOrders,
            previousStats.totalOrders
          ),
        },
        totalVariants: {
          value: currentStats.totalVariants.toLocaleString("sv-SE"),
          ...calculateTrend(
            currentStats.totalVariants,
            previousStats.totalVariants
          ),
        },
      },
      diagrams: {
        orderTimeline,
        revenueTimeline,
      },
      tables: {
        topProducts,
        worstProducts,
      },
    };

    // Cache the response
    responseCache.set(cacheKey, {
      data: response,
      timestamp: Date.now(),
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in statistics API:", error);
    return NextResponse.json(
      { error: "Failed to fetch statistics" },
      { status: 500 }
    );
  }
}

function calculatePeriodStats(orders: Order[]) {
  const totalRevenue = orders.reduce(
    (sum, order) => sum + parseFloat(order.total),
    0
  );

  // Count total variants sold
  const totalVariants = orders.reduce(
    (sum, order) =>
      sum +
      order.line_items.reduce((itemSum, item) => itemSum + item.quantity, 0),
    0
  );

  return {
    totalRevenue,
    averageOrderValue: totalRevenue / (orders.length || 1),
    totalOrders: orders.length,
    totalVariants,
  };
}

function calculateTrend(
  current: number,
  previous: number
): {
  trend: "positive" | "negative" | "neutral";
  change: number;
} {
  if (previous === 0) return { trend: "neutral", change: 0 };

  const change = ((current - previous) / previous) * 100;
  return {
    trend: change > 0 ? "positive" : change < 0 ? "negative" : "neutral",
    change: Number(change.toFixed(1)),
  };
}

function generateTimeline(
  currentOrders: Order[],
  previousOrders: Order[],
  start: string,
  end: string,
  previousStart: string,
  previousEnd: string
) {
  const current = groupOrdersByDate(
    currentOrders,
    new Date(start),
    new Date(end)
  );
  const previous = groupOrdersByDate(
    previousOrders,
    new Date(previousStart),
    new Date(previousEnd)
  );

  return {
    current,
    previous,
  };
}

function generateRevenueTimeline(
  currentOrders: Order[],
  previousOrders: Order[],
  start: string,
  end: string,
  previousStart: string,
  previousEnd: string
) {
  const current = groupRevenueByDate(
    currentOrders,
    new Date(start),
    new Date(end)
  );
  const previous = groupRevenueByDate(
    previousOrders,
    new Date(previousStart),
    new Date(previousEnd)
  );

  return {
    current,
    previous,
  };
}

function groupOrdersByDate(orders: Order[], startDate: Date, endDate: Date) {
  const timeline: { date: string; value: number }[] = [];

  // Create a map to aggregate orders by date
  const ordersByDate = new Map<string, number>();

  // Fill in all dates in the range with 0
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const dateStr = currentDate.toISOString().split("T")[0];
    ordersByDate.set(dateStr, 0);
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Count orders for each date
  orders.forEach((order) => {
    const dateStr = new Date(order.date_created).toISOString().split("T")[0];
    if (ordersByDate.has(dateStr)) {
      ordersByDate.set(dateStr, (ordersByDate.get(dateStr) || 0) + 1);
    }
  });

  // Convert map to array and sort by date
  return Array.from(ordersByDate.entries())
    .map(([date, value]) => ({ date, value }))
    .sort((a, b) => a.date.localeCompare(b.date));
}

function groupRevenueByDate(orders: Order[], startDate: Date, endDate: Date) {
  const timeline: { date: string; value: number }[] = [];

  // Create a map to aggregate revenue by date
  const revenueByDate = new Map<string, number>();

  // Fill in all dates in the range with 0
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const dateStr = currentDate.toISOString().split("T")[0];
    revenueByDate.set(dateStr, 0);
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Sum revenue for each date
  orders.forEach((order) => {
    const dateStr = new Date(order.date_created).toISOString().split("T")[0];
    if (revenueByDate.has(dateStr)) {
      revenueByDate.set(
        dateStr,
        (revenueByDate.get(dateStr) || 0) + parseFloat(order.total)
      );
    }
  });

  // Convert map to array and sort by date
  return Array.from(revenueByDate.entries())
    .map(([date, value]) => ({ date, value }))
    .sort((a, b) => a.date.localeCompare(b.date));
}

async function fetchTopProducts(start: string, end: string) {
  try {
    const orders = await fetchAllOrders(start, end);
    if (!orders.length) return [];

    // Create a map to aggregate product data first
    const productAggregates = new Map();

    orders.forEach((order) => {
      order.line_items.forEach((item) => {
        const existing = productAggregates.get(item.product_id) || {
          quantity: 0,
          revenue: 0,
          name: item.name,
        };

        existing.quantity += item.quantity;
        existing.revenue += parseFloat(item.total);
        productAggregates.set(item.product_id, existing);
      });
    });

    // Sort and get top 5 products by revenue instead of 10
    const topProductIds = Array.from(productAggregates.entries())
      .sort((a, b) => b[1].revenue - a[1].revenue)
      .slice(0, 5) // Changed from 10 to 5
      .map(([id]) => id);

    // Only fetch details for top 5 products
    const products = await Promise.all(
      topProductIds.map((id) =>
        safeWooCommerceRequest<WooCommerceProduct>(`products/${id}`)
      )
    );

    return products
      .filter((product): product is WooCommerceProduct => product !== null)
      .map((product) => {
        const stats = productAggregates.get(product.id);
        return {
          product: product.name,
          type: product.type,
          variantCount: product.variations?.length || 0,
          variantInfo: product.variations?.length
            ? `(${product.variations.length} varianter)`
            : "",
          totalSales: stats.quantity.toLocaleString("sv-SE"),
          totalRevenue: `${Number(stats.revenue).toLocaleString("sv-SE")} kr`,
        };
      });
  } catch (error) {
    console.error("Error in fetchTopProducts:", error);
    return [];
  }
}

async function fetchWorstProducts(start: string, end: string) {
  try {
    const orders = await fetchAllOrders(start, end);
    if (!orders.length) return [];

    // Create a map to aggregate product data
    const productAggregates = new Map();

    orders.forEach((order) => {
      order.line_items.forEach((item) => {
        const existing = productAggregates.get(item.product_id) || {
          quantity: 0,
          revenue: 0,
          name: item.name,
        };

        existing.quantity += item.quantity;
        existing.revenue += parseFloat(item.total);
        productAggregates.set(item.product_id, existing);
      });
    });

    // Sort by revenue (ascending) and get bottom 5 products
    const worstProductIds = Array.from(productAggregates.entries())
      .sort((a, b) => a[1].revenue - b[1].revenue)
      .slice(0, 5)
      .map(([id]) => id);

    // Fetch details for worst 5 products
    const products = await Promise.all(
      worstProductIds.map((id) =>
        safeWooCommerceRequest<WooCommerceProduct>(`products/${id}`)
      )
    );

    return products
      .filter((product): product is WooCommerceProduct => product !== null)
      .map((product) => {
        const stats = productAggregates.get(product.id);
        return {
          product: product.name,
          type: product.type,
          variantCount: product.variations?.length || 0,
          variantInfo: product.variations?.length
            ? `(${product.variations.length} varianter)`
            : "",
          totalSales: stats.quantity.toLocaleString("sv-SE"),
          totalRevenue: `${Number(stats.revenue).toLocaleString("sv-SE")} kr`,
        };
      });
  } catch (error) {
    console.error("Error in fetchWorstProducts:", error);
    return [];
  }
}
