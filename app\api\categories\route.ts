import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

const WC_API_URL = process.env.NEXT_PUBLIC_WC_API_URL;
const WC_CONSUMER_KEY = process.env.WC_CONSUMER_KEY;
const WC_CONSUMER_SECRET = process.env.WC_CONSUMER_SECRET;

export async function GET() {
  if (!WC_CONSUMER_KEY || !WC_CONSUMER_SECRET || !WC_API_URL) {
    console.error("Missing WooCommerce API credentials");
    return NextResponse.json(
      { error: "WooCommerce API credentials not configured" },
      { status: 500 }
    );
  }

  const auth = Buffer.from(`${WC_CONSUMER_KEY}:${WC_CONSUMER_SECRET}`).toString(
    "base64"
  );

  try {
    // Fetch all product categories from WooCommerce
    const apiUrl = `${WC_API_URL}/products/categories?per_page=100`;
    const response = await fetch(apiUrl, {
      headers: {
        Authorization: `Basic ${auth}`,
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(`WooCommerce API error: ${response.statusText}`);
    }

    const categories = await response.json();

    if (!Array.isArray(categories)) {
      throw new Error("Invalid category data received");
    }

    // Return the categories
    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to fetch categories",
      },
      { status: 500 }
    );
  }
}
