import { FC } from "react";

interface StatCardProps {
  title: string;
  value: string | number;
  trend?: "positive" | "negative" | "neutral";
  change?: number;
  icon?: string;
  prefix?: string;
  suffix?: string;
}

export const StatCard: FC<StatCardProps> = ({
  title,
  value,
  trend,
  change,
  icon,
  prefix = "",
  suffix = "",
}) => {
  const formattedValue =
    typeof value === "number"
      ? new Intl.NumberFormat("sv-SE", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
          useGrouping: true,
        }).format(value)
      : value;

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <h3 className="text-2xl font-semibold mt-1">
            {prefix}
            {formattedValue}
            {suffix}
          </h3>
          {trend && (
            <div className="flex items-center mt-2">
              <span
                className={`flex items-center text-sm font-medium ${
                  trend === "positive"
                    ? "text-green-600"
                    : trend === "negative"
                    ? "text-red-600"
                    : "text-gray-600"
                }`}
              >
                <span className="text-lg mr-1">
                  {trend === "positive"
                    ? "↑"
                    : trend === "negative"
                    ? "↓"
                    : "−"}
                </span>
                {typeof change === "number" && (
                  <span className="flex items-center">
                    {Math.abs(change).toFixed(1)}%
                    <span
                      className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                        trend === "positive"
                          ? "bg-green-100"
                          : trend === "negative"
                          ? "bg-red-100"
                          : "bg-gray-100"
                      }`}
                    >
                      {trend === "positive"
                        ? "Ökning"
                        : trend === "negative"
                        ? "Minskning"
                        : "Oförändrad"}
                    </span>
                  </span>
                )}
              </span>
            </div>
          )}
        </div>
        {icon && <span className="text-2xl">{icon}</span>}
      </div>
    </div>
  );
};
