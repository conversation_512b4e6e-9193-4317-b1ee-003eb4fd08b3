export const translations = {
  sv: {
    filters: {
      search: {
        placeholder: "Sök produkter...",
      },
      categories: {
        all: "Alla kategorier",
        loading: "Laddar kategorier...",
      },
      stockStatus: {
        all: "Alla",
        inStock: "I lager",
        outOfStock: "Slut i lager",
        onBackorder: "I restorder",
      },
      reset: "Återställ filter",
    },
    navigation: {
      dashboard: "Översikt",
      statistics: "Statistik",
      home: "KSWooSync",
    },
    statistics: {
      title: "Produktstatistik",
      productTypes: "Produkttyper",
      financialOverview: "Ekonomisk översikt",
      topProducts: "Mest lagerförda produkter",
      sales: {
        title: "Försäljningsöversikt",
        total: "Total försäljning",
        averageOrder: "Genomsnittlig order",
      },
      orders: {
        total: "Totalt antal ordrar",
        timeline: "Ordertidslinje",
      },
      products: {
        title: "Produktöversikt",
        topSellers: "<PERSON>ps<PERSON>ljare",
        lowStock: "Produkter med lågt lager",
      },
      metrics: {
        totalProducts: "Totalt antal produkter",
        totalStock: "Totalt lagersaldo",
        lowStock: "Produkter med lågt lager",
        outOfStock: "Slut i lager",
        averagePrice: "Genomsnittspris",
        totalStockValue: "Totalt lagervärde",
        units: "st",
      },
      loading: "Laddar statistik...",
      error: "Ett fel uppstod vid laddning av statistik",
      retry: "Försök igen",
      customers: {
        title: "Kundöversikt",
        total: "Totalt antal kunder",
        new: "Nya kunder",
        returning: "Återkommande kunder",
        byRegion: "Kunder per region",
        top: "Topplista kunder",
      },
      payment: {
        title: "Betalning och leverans",
        methods: "Betalningsmetoder",
      },
      shipping: {
        methods: "Leveransmetoder",
      },
      financial: {
        title: "Finansiell översikt",
      },
      discounts: {
        topCoupons: "Populäraste rabattkoderna",
      },
      taxes: {
        byRegion: "Moms per region",
      },
    },
    dashboard: {
      title: "Produktöversikt",
      subtitle: "Hantera dina WooCommerce-produkter",
      loading: "Laddar produkter...",
      error: "Ett fel uppstod",
      retry: "Försök igen",
      noProducts: "Inga produkter hittades",
      refresh: "Uppdatera",
      table: {
        product: "Produkt",
        type: "Typ",
        stockStatus: "Lagerstatus",
        actions: "Åtgärder",
        price: "Pris",
        quantity: "Antal",
        sku: "Artikelnummer",
      },
    },
    pagination: {
      showing: "Visar",
      of: "av",
      items: "produkter",
      page: "Sida",
      next: "Nästa",
      previous: "Föregående",
    },
    common: {
      search: "Sök produkter...",
      currency: "kr",
      loading: "Laddar...",
      error: "Fel",
      retry: "Försök igen",
      save: "Spara",
      saving: "Sparar...",
      cancel: "Avbryt",
      saveChanges: "Spara ändringar",
      name: "Namn",
      sales: "Försäljning",
      views: "Visningar",
      stock: "Lager",
      threshold: "Gränsvärde",
      code: "Kod",
      uses: "Användningar",
      amount: "Belopp",
      orders: "Ordrar",
      totalSpent: "Totalt spenderat",
    },
    products: {
      edit: {
        title: "Redigera produkt",
      },
      fields: {
        name: "Produktnamn",
        sku: "Artikelnummer",
        regularPrice: "Ordinarie pris",
        salePrice: "Kampanjpris",
        description: "Beskrivning",
        stockQuantity: "Lagerantal",
        stockStatus: "Lagerstatus",
        manageStock: "Hantera lager?",
      },
      type: {
        variable: "Variabel",
        simple: "Enkel",
        variation: "Variant",
        "gift-card": "Presentkort",
      },
      stock: {
        inStock: "I lager",
        outOfStock: "Slut i lager",
        lowStock: "Lågt lager",
        onBackorder: "I restorder",
        variationCount: {
          out: "slut",
          low: "lågt",
          ok: "ok",
        },
      },
      actions: {
        showVariations: "Visa varianter",
        hideVariations: "Dölj varianter",
        edit: "Redigera",
      },
      errors: {
        updateFailed: "Det gick inte att uppdatera produkten. Försök igen.",
      },
      updateError: "Det gick inte att uppdatera lagersaldot. Försök igen.",
      pendingChanges: "{{count}} väntande ändringar",
    },
  },
  en: {
    filters: {
      search: {
        placeholder: "Search products...",
      },
      categories: {
        all: "All Categories",
        loading: "Loading categories...",
      },
      stockStatus: {
        all: "All Stock Status",
        inStock: "In Stock",
        outOfStock: "Out of Stock",
        onBackorder: "On Backorder",
      },
      reset: "Reset Filters",
    },
    navigation: {
      dashboard: "Dashboard",
      statistics: "Statistics",
      home: "KSWooSync",
    },
    statistics: {
      title: "Product Statistics",
      productTypes: "Product Types",
      financialOverview: "Financial Overview",
      topProducts: "Top Products by Stock Level",
      sales: {
        title: "Sales Overview",
        total: "Total Sales",
        averageOrder: "Average Order",
      },
      orders: {
        total: "Total Orders",
        timeline: "Order Timeline",
      },
      products: {
        title: "Product Overview",
        topSellers: "Top Sellers",
        lowStock: "Low Stock Products",
      },
      metrics: {
        totalProducts: "Total Products",
        totalStock: "Total Stock",
        lowStock: "Low Stock Products",
        outOfStock: "Out of Stock",
        averagePrice: "Average Price",
        totalStockValue: "Total Stock Value",
        units: "units",
      },
      loading: "Loading statistics...",
      error: "Error loading statistics",
      retry: "Try again",
      customers: {
        title: "Customer Overview",
        total: "Total Customers",
        new: "New Customers",
        returning: "Returning Customers",
        byRegion: "Customers by Region",
        top: "Top Customers",
      },
      payment: {
        title: "Payment and Shipping",
        methods: "Payment Methods",
      },
      shipping: {
        methods: "Shipping Methods",
      },
      financial: {
        title: "Financial Overview",
      },
      discounts: {
        topCoupons: "Top Discount Codes",
      },
      taxes: {
        byRegion: "Taxes by Region",
      },
      users: {
        title: "User Overview",
        total: "Total Users",
        new: "New Users",
        roles: "User Roles",
        byRole: "Users by Role",
        recent: "Recently Registered Users",
      },
    },
    dashboard: {
      title: "Product Dashboard",
      subtitle: "Manage your WooCommerce products",
      loading: "Loading products...",
      error: "Error",
      retry: "Retry",
      noProducts: "No products found",
      refresh: "Refresh",
      table: {
        product: "Product",
        type: "Type",
        stockStatus: "Stock Status",
        actions: "Actions",
        price: "Price",
        quantity: "Quantity",
        sku: "SKU",
      },
    },
    pagination: {
      showing: "Showing",
      of: "of",
      items: "items",
      page: "Page",
      next: "Next",
      previous: "Previous",
    },
    common: {
      search: "Search products...",
      currency: "$",
      loading: "Loading...",
      error: "Error",
      retry: "Retry",
      save: "Save",
      saving: "Saving...",
      cancel: "Cancel",
      saveChanges: "Save Changes",
      name: "Name",
      role: "Role",
      registered: "Registered Date",
      sales: "Sales",
      views: "Views",
      stock: "Stock",
      threshold: "Threshold",
      code: "Code",
      uses: "Uses",
      amount: "Amount",
      orders: "Orders",
      totalSpent: "Total Spent",
    },
    products: {
      edit: {
        title: "Edit Product",
      },
      fields: {
        name: "Product Name",
        sku: "SKU",
        regularPrice: "Regular Price",
        salePrice: "Sale Price",
        description: "Description",
        stockQuantity: "Stock Quantity",
        stockStatus: "Stock Status",
        manageStock: "Manage stock?",
      },
      type: {
        variable: "Variable",
        simple: "Simple",
        variation: "Variation",
        "gift-card": "Gift Card",
      },
      stock: {
        inStock: "In Stock",
        outOfStock: "Out of Stock",
        lowStock: "Low Stock",
        onBackorder: "On Backorder",
        variationCount: {
          out: "out",
          low: "low",
          ok: "ok",
        },
      },
      actions: {
        showVariations: "Show Variations",
        hideVariations: "Hide Variations",
        edit: "Edit",
      },
      errors: {
        updateFailed: "Failed to update product. Please try again.",
      },
      updateError: "Failed to update stock. Please try again.",
      pendingChanges: "{{count}} pending changes",
    },
  },
};

export type Language = "sv" | "en";
