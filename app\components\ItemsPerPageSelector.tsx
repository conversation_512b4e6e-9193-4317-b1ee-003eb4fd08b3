"use client";

import { useState } from "react";
import { useUserPreferences } from "../hooks/useUserPreferences";

interface ItemsPerPageSelectorProps {
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  onImmediateUpdate?: (itemsPerPage: number) => Promise<void>;
  className?: string;
}

const ITEMS_PER_PAGE_OPTIONS = [5, 10, 20, 25, 50, 100];

export default function ItemsPerPageSelector({
  onItemsPerPageChange,
  onImmediateUpdate,
  className = "",
}: ItemsPerPageSelectorProps) {
  const { preferences, updateItemsPerPage, isLoading } = useUserPreferences();
  const [isUpdating, setIsUpdating] = useState(false);

  const handleChange = async (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newItemsPerPage = parseInt(event.target.value, 10);
    console.log(
      "ItemsPerPageSelector: Changing from",
      preferences.itemsPerPage,
      "to",
      newItemsPerPage
    );

    if (newItemsPerPage === preferences.itemsPerPage) {
      console.log("ItemsPerPageSelector: No change needed");
      return; // No change needed
    }

    setIsUpdating(true);

    try {
      console.log("ItemsPerPageSelector: Starting update process");

      // Call immediate update callback first with the new value (before API call)
      if (onImmediateUpdate) {
        console.log(
          "ItemsPerPageSelector: Calling onImmediateUpdate with new value"
        );
        await onImmediateUpdate(newItemsPerPage);
        console.log("ItemsPerPageSelector: onImmediateUpdate completed");
      }

      // Update user preferences (this will do optimistic update)
      const success = await updateItemsPerPage(newItemsPerPage);
      console.log("ItemsPerPageSelector: updateItemsPerPage result:", success);

      if (success) {
        // Call the regular callback for any additional handling
        if (onItemsPerPageChange) {
          console.log("ItemsPerPageSelector: Calling onItemsPerPageChange");
          onItemsPerPageChange(newItemsPerPage);
        }

        console.log(
          "ItemsPerPageSelector: Update process completed successfully"
        );
      } else {
        console.error("ItemsPerPageSelector: Failed to update preferences");
      }
    } catch (error) {
      console.error(
        "ItemsPerPageSelector: Failed to update items per page:",
        error
      );
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <span className="text-sm text-gray-600">Items per page:</span>
        <div className="w-16 h-8 bg-gray-200 animate-pulse rounded"></div>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <label htmlFor="items-per-page" className="text-sm text-gray-600">
        Items per page:
      </label>
      <select
        id="items-per-page"
        value={preferences.itemsPerPage}
        onChange={handleChange}
        disabled={isUpdating}
        className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {ITEMS_PER_PAGE_OPTIONS.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
      {isUpdating && (
        <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
      )}
    </div>
  );
}
