import { NextResponse } from "next/server";
// headers is not used, so I'll remove it for now. If it's needed later, it can be re-added.
// import { headers } from "next/headers"; 
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]/route"; // Adjusted path

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: "Unauthorized. Please log in." }, { status: 401 });
  }

  // Original API logic starts here
  // User is authenticated, proceed to fetch orders.
  // You can use session.user if needed, e.g., for user-specific orders.
  // console.log("Authenticated user:", session.user);

  const searchParams = new URL(request.url).searchParams;
  const page = Number(searchParams.get("page")) || 1;
  const per_page = Number(searchParams.get("per_page")) || 100;

  const after = searchParams.get("after");
  const before = searchParams.get("before");

  if (
    !process.env.WC_CONSUMER_KEY ||
    !process.env.WC_CONSUMER_SECRET ||
    !process.env.NEXT_PUBLIC_WC_API_URL
  ) {
    return NextResponse.json(
      { error: "WooCommerce API credentials not configured" },
      { status: 500 }
    );
  }

  const auth = Buffer.from(
    `${process.env.WC_CONSUMER_KEY}:${process.env.WC_CONSUMER_SECRET}`
  ).toString("base64");

  let apiUrl = `${process.env.NEXT_PUBLIC_WC_API_URL}/orders?page=${page}&per_page=${per_page}`;
  if (after) apiUrl += `&after=${after}`;
  if (before) apiUrl += `&before=${before}`;

  try {
    const response = await fetch(apiUrl, {
      headers: {
        Authorization: `Basic ${auth}`,
      },
    });

    if (!response.ok) {
      // Log more details for server-side debugging
      const errorBody = await response.text();
      console.error(`HTTP error! status: ${response.status}, body: ${errorBody}, url: ${apiUrl}`);
      return NextResponse.json(
        { error: `Failed to fetch orders from WooCommerce. Status: ${response.status}` },
        { status: response.status } // Propagate the status
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error("Error fetching orders:", error);
    return NextResponse.json(
      { error: "An internal server error occurred while fetching orders." },
      { status: 500 }
    );
  }
}
