"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

interface Invitation {
  id: string;
  email: string;
  role: string;
  token: string;
  createdAt: string;
  expiresAt: string;
  isUsed: boolean;
  usedAt?: string;
  creator: {
    id: string;
    name: string;
    email: string;
  };
}

export default function AdminInvitationsPage() {
  const { data: session, status } = useSession();
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Create invitation form state
  const [newEmail, setNewEmail] = useState("");
  const [newRole, setNewRole] = useState("user");
  const [isCreating, setIsCreating] = useState(false);
  const [createdInvitation, setCreatedInvitation] = useState<any>(null);

  const fetchInvitations = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/admin/invitations");
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to fetch invitations");
      }
      const data = await response.json();
      setInvitations(data);
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (status === "authenticated" && session?.user?.role === "admin") {
      fetchInvitations();
    } else if (status === "authenticated") {
      setError("Access Denied: You do not have permission to view this page.");
      setIsLoading(false);
    }
  }, [status, session]);

  const handleCreateInvitation = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);
    setError(null);

    try {
      const response = await fetch("/api/admin/invitations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: newEmail, role: newRole }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to create invitation");
      }

      setCreatedInvitation(data);
      setNewEmail("");
      setNewRole("user");
      setShowCreateForm(false);
      fetchInvitations(); // Refresh the list
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsCreating(false);
    }
  };

  const handleRevokeInvitation = async (invitationId: string) => {
    if (!window.confirm("Are you sure you want to revoke this invitation?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/invitations/${invitationId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to revoke invitation");
      }

      fetchInvitations(); // Refresh the list
    } catch (err) {
      setError((err as Error).message);
    }
  };

  const copyInvitationLink = (token: string) => {
    const link = `${window.location.origin}/signup?token=${token}`;
    navigator.clipboard.writeText(link);
    alert("Invitation link copied to clipboard!");
  };

  if (status === "loading" || isLoading) {
    return (
      <div className="container mx-auto p-4 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>Loading invitations...</p>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto p-4 text-center">
        Redirecting to login...
      </div>
    );
  }

  if (session?.user?.role !== "admin") {
    return (
      <div className="container mx-auto p-4 text-center text-red-500">
        Access Denied: You do not have permission to view this page.
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4 text-red-500">Error: {error}</div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <a
            href="/admin/users"
            className="text-blue-600 hover:text-blue-800 text-sm mb-2 inline-block"
          >
            ← Back to User Management
          </a>
          <h1 className="text-2xl font-bold text-gray-800">
            Invitation Management
          </h1>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Create Invitation
        </button>
      </div>

      {/* Success message for created invitation */}
      {createdInvitation && (
        <div className="mb-6 p-4 bg-green-100 border border-green-400 rounded-lg">
          <h3 className="font-semibold text-green-800 mb-2">
            Invitation Created Successfully!
          </h3>
          <p className="text-green-700 mb-2">
            Invitation sent to:{" "}
            <strong>{createdInvitation.invitation.email}</strong>
          </p>
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={createdInvitation.invitationLink}
              readOnly
              className="flex-1 px-3 py-2 border border-gray-300 rounded bg-gray-50 text-sm"
            />
            <button
              onClick={() =>
                copyInvitationLink(createdInvitation.invitation.token)
              }
              className="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
            >
              Copy Link
            </button>
          </div>
          <button
            onClick={() => setCreatedInvitation(null)}
            className="mt-2 text-sm text-green-600 hover:text-green-800"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Create invitation modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg w-full max-w-md">
            <h2 className="text-xl font-semibold mb-4">
              Create New Invitation
            </h2>
            <form onSubmit={handleCreateInvitation}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={newEmail}
                  onChange={(e) => setNewEmail(e.target.value)}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Role
                </label>
                <select
                  value={newRole}
                  onChange={(e) => setNewRole(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="user">User</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
              <div className="flex gap-2">
                <button
                  type="submit"
                  disabled={isCreating}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {isCreating ? "Creating..." : "Create Invitation"}
                </button>
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Invitations table */}
      <div className="overflow-x-auto bg-white shadow-md rounded-lg">
        <table className="min-w-full table-auto">
          <thead className="bg-gray-100">
            <tr>
              {[
                "Email",
                "Role",
                "Status",
                "Created By",
                "Created At",
                "Expires At",
                "Actions",
              ].map((header) => (
                <th
                  key={header}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {invitations.map((invitation) => (
              <tr key={invitation.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {invitation.email}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${
                      invitation.role === "admin"
                        ? "bg-purple-100 text-purple-800"
                        : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    {invitation.role}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {invitation.isUsed ? (
                    <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                      Used
                    </span>
                  ) : new Date() > new Date(invitation.expiresAt) ? (
                    <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                      Expired
                    </span>
                  ) : (
                    <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {invitation.creator.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(invitation.createdAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(invitation.expiresAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {!invitation.isUsed &&
                    new Date() <= new Date(invitation.expiresAt) && (
                      <>
                        <button
                          onClick={() => copyInvitationLink(invitation.token)}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          Copy Link
                        </button>
                        <button
                          onClick={() => handleRevokeInvitation(invitation.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Revoke
                        </button>
                      </>
                    )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {invitations.length === 0 && (
        <p className="text-center text-gray-500 mt-4">No invitations found.</p>
      )}
    </div>
  );
}
