interface DateRangePickerProps {
  value: {
    start: Date;
    end: Date;
  };
  onChange: (range: { start: Date; end: Date }) => void;
}

export function DateRangePicker({
  value = {
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Default to 1 week
    end: new Date(),
  },
  onChange,
}: DateRangePickerProps) {
  // Ensure we have valid Date objects
  const safeStart =
    value?.start instanceof Date
      ? value.start
      : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  const safeEnd = value?.end instanceof Date ? value.end : new Date();

  return (
    <div className="flex gap-2">
      <input
        type="date"
        value={safeStart.toISOString().split("T")[0]}
        onChange={(e) =>
          onChange({ ...value, start: new Date(e.target.value) })
        }
        className="border rounded px-3 py-2 text-sm"
      />
      <input
        type="date"
        value={safeEnd.toISOString().split("T")[0]}
        onChange={(e) => onChange({ ...value, end: new Date(e.target.value) })}
        className="border rounded px-3 py-2 text-sm"
      />
    </div>
  );
}
