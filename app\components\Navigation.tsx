"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import LanguageSwitcher from "./LanguageSwitcher";
import { useSession, signOut } from "next-auth/react";

export default function Navigation() {
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession();
  const isAdmin = (session?.user as any)?.role === 'admin';

  // Prefetch the routes when the component mounts
  useEffect(() => {
    if (status === 'authenticated') {
      router.prefetch("/dashboard");
      router.prefetch("/statistics");
      if (isAdmin) {
        router.prefetch("/admin/users");
      }
    }
  }, [router, status, isAdmin]);

  return (
    <nav className="bg-white shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-800">
                WooSync
              </Link>
            </div>
            {/* Conditional rendering for main navigation links */}
            {status === 'authenticated' && (
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <Link
                  href="/dashboard"
                  prefetch={true}
                  className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                    pathname === "/dashboard"
                      ? "border-blue-500 text-gray-900"
                      : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  }`}
                >
                  Dashboard
                </Link>
                <Link
                  href="/statistics"
                  prefetch={true}
                  className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                    pathname === "/statistics"
                      ? "border-blue-500 text-gray-900"
                      : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  }`}
                >
                  Statistics
                </Link>
                {/* Admin Link */}
                {isAdmin && (
                  <Link
                    href="/admin/users"
                    prefetch={true}
                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                      pathname === "/admin/users" || pathname?.startsWith("/admin")
                        ? "border-blue-500 text-gray-900"
                        : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                    }`}
                  >
                    Admin Users
                  </Link>
                )}
              </div>
            )}
          </div>

          {/* Right side of the navbar: Language Switcher and Auth Links */}
          <div className="flex items-center">
            <LanguageSwitcher />
            <div className="ml-4 flex items-center"> {/* Container for auth links */}
              {status === 'loading' ? (
                <div className="text-sm text-gray-500">Loading...</div>
              ) : session ? (
                <>
                  <span className="mr-3 text-sm text-gray-700 hidden sm:inline">
                    Hi, {session.user?.name || session.user?.email} {isAdmin ? '(Admin)' : ''}
                  </span>
                  <button
                    onClick={() => signOut({ callbackUrl: '/' })}
                    className="text-sm font-medium text-blue-600 hover:text-blue-500"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link href="/login" className="text-sm font-medium text-gray-700 hover:text-gray-900 mr-4">
                    Login
                  </Link>
                  <Link href="/signup" className="text-sm font-medium text-blue-600 hover:text-blue-500">
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
