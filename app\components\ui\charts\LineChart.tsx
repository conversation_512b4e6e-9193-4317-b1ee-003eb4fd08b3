import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  Scale,
  ScaleOptionsByType,
  CoreScaleOptions,
  Tick,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface TimelineData {
  date: string;
  value: number;
}

interface LineChartProps {
  title: string;
  currentData: TimelineData[];
  previousData?: TimelineData[];
  yAxisLabel?: string;
}

export function LineChart({
  title,
  currentData,
  previousData,
  yAxisLabel,
}: LineChartProps) {
  // Calculate the maximum value to set appropriate y-axis scale
  const allValues = [
    ...currentData.map((item) => item.value),
    ...(previousData?.map((item) => item.value) || []),
  ];
  const maxValue = Math.max(...allValues);
  const padding = Math.ceil(maxValue * 0.1); // Add 10% padding

  const options: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: "bold",
        },
        padding: 20,
      },
      tooltip: {
        mode: "index",
        intersect: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          maxRotation: 0,
          minRotation: 0,
          maxTicksLimit: 10, // Limit the number of x-axis labels
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "#f0f0f0",
        },
        title: {
          display: !!yAxisLabel,
          text: yAxisLabel || "",
        },
        min: 0,
        max: maxValue + padding, // Set max with padding
        ticks: {
          stepSize: Math.ceil((maxValue + padding) / 10), // Create about 10 steps
          callback: function (
            this: Scale<CoreScaleOptions>,
            tickValue: string | number,
            index: number,
            ticks: Tick[]
          ): string | number | string[] | number[] | null | undefined {
            const value = Number(tickValue);
            return value.toLocaleString("sv-SE");
          },
        },
      },
    },
    interaction: {
      mode: "nearest",
      axis: "x",
      intersect: false,
    },
  };

  const data = {
    labels: currentData.map((item) => item.date),
    datasets: [
      {
        label: "Current Period",
        data: currentData.map((item) => item.value),
        borderColor: "rgb(53, 162, 235)",
        backgroundColor: "rgba(53, 162, 235, 0.5)",
      },
      ...(previousData
        ? [
            {
              label: "Previous Period",
              data: previousData.map((item) => item.value),
              borderColor: "rgb(255, 99, 132)",
              backgroundColor: "rgba(255, 99, 132, 0.5)",
            },
          ]
        : []),
    ],
  };

  return (
    <div style={{ height: "400px" }}>
      <Line options={options} data={data} />
    </div>
  );
}
