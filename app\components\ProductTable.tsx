"use client";

import { Product, ProductVariation } from "@/types/index";
import { updateProduct } from "../utils/api-client";
import { useState, useEffect } from "react";
import React from "react";
import { useLanguage } from "../context/LanguageContext";
import ProductEditModal from "./ProductEditModal";

interface ProductTableProps {
  products: Product[];
  onProductUpdate: () => void;
}

interface PendingUpdate {
  productId: number;
  variationId: number;
  newStock: number;
}

const getVariationStockSummary = (variations: ProductVariation[]) => {
  // Use ProductVariation type
  const zeroStock = variations.filter(
    (v) => !v.stock_quantity || v.stock_quantity <= 0 // Include 0
  ).length;
  const lowStock = variations.filter(
    (v) => v.stock_quantity && v.stock_quantity > 0 && v.stock_quantity < 3 // Assuming low stock threshold is < 3
  ).length;
  const normalStock = variations.filter(
    (v) => v.stock_quantity && v.stock_quantity >= 3
  ).length;

  return { zeroStock, lowStock, normalStock };
};

// Helper function to get status text and style based on quantity
const getStockStatusInfo = (
  quantity: number | null,
  t: Function // Assuming t function takes only one argument (key)
): { text: string; className: string } => {
  let statusKey: string;
  let className: string;

  if (quantity === null || quantity <= 0) {
    statusKey = "outOfStock";
    className = "bg-red-100 text-red-800";
  } else if (quantity < 3) {
    // Assuming low stock threshold < 3
    statusKey = "lowStock"; // Ensure 'products.stock.lowStock' exists in translations
    className = "bg-yellow-100 text-yellow-800";
  } else {
    statusKey = "inStock";
    className = "bg-green-100 text-green-800";
  }
  // Note: 'onbackorder' status from the API isn't directly represented by quantity alone.

  try {
    // Try to get the translation
    return {
      text: t(`products.stock.${statusKey}`),
      className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${className}`,
    };
  } catch (error) {
    // Fallback if translation fails
    console.warn(
      `Translation error for key: products.stock.${statusKey}`,
      error
    );
    return {
      text:
        statusKey === "outOfStock"
          ? "Out of Stock"
          : statusKey === "lowStock"
          ? "Low Stock"
          : statusKey === "inStock"
          ? "In Stock"
          : statusKey,
      className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${className}`,
    };
  }
};

export default function ProductTable({
  products,
  onProductUpdate,
}: ProductTableProps) {
  const { t } = useLanguage();
  const [updating, setUpdating] = useState<number | null>(null);
  const [variations, setVariations] = useState<
    Record<number, ProductVariation[]>
  >({});
  const [expandedProducts, setExpandedProducts] = useState<number[]>([]);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [pendingUpdates, setPendingUpdates] = useState<PendingUpdate[]>([]);
  const [isLoading, setIsLoading] = useState(false); // For save all button
  const [updatingItemId, setUpdatingItemId] = useState<number | null>(null); // For individual variation save spinner
  const [initialStockValues, setInitialStockValues] = useState<
    Record<string, number>
  >({});
  const [loadingVariations, setLoadingVariations] = useState<
    Record<number, boolean>
  >({}); // Track loading state per product
  const [errorVariations, setErrorVariations] = useState<
    Record<number, boolean>
  >({}); // Track error state per product

  // --- Updated useEffect to load variations for ALL variable products ---
  useEffect(() => {
    const loadVariations = async (productId: number) => {
      // Check if already loading or loaded to prevent redundant fetches
      if (loadingVariations[productId] || variations[productId]) return;

      setLoadingVariations((prev) => ({ ...prev, [productId]: true }));
      setErrorVariations((prev) => ({ ...prev, [productId]: false })); // Reset error state
      try {
        const response = await fetch(`/api/products/${productId}/variations`);
        if (!response.ok)
          throw new Error(
            `Failed to fetch variations (status: ${response.status})`
          );
        const data = await response.json();

        // Ensure data is an array before processing
        if (!Array.isArray(data)) {
          console.warn(
            `Received non-array data for variations of product ${productId}:`,
            data
          );
          setVariations((prev) => ({ ...prev, [productId]: [] })); // Set empty array if data is invalid
          setErrorVariations((prev) => ({ ...prev, [productId]: true })); // Set error state
          return; // Stop processing for this product
        }

        setVariations((prev) => ({ ...prev, [productId]: data }));

        // Set initial stock values when variations load
        const initialValuesUpdate: Record<string, number> = {};
        data.forEach((variation: ProductVariation) => {
          const key = `${productId}-${variation.id}`;
          initialValuesUpdate[key] = variation.stock_quantity ?? 0;
        });
        setInitialStockValues((prev) => ({ ...prev, ...initialValuesUpdate }));
      } catch (error) {
        console.error(
          `Failed to load variations for product ${productId}:`,
          error
        );
        setErrorVariations((prev) => ({ ...prev, [productId]: true })); // Set error state
        setVariations((prev) => ({ ...prev, [productId]: [] })); // Set empty array on error to prevent issues
      } finally {
        setLoadingVariations((prev) => ({ ...prev, [productId]: false }));
      }
    };

    // Iterate through products prop and load variations for variable ones
    const currentProductIds = new Set<number>();
    products.forEach((product) => {
      currentProductIds.add(product.id);
      if (product.type === "variable") {
        loadVariations(product.id);
      }
    });

    // Clear variation data/state for products no longer in the list
    const cleanupIds = (prevState: Record<number, any>) => {
      const nextState: Record<number, any> = {};
      for (const productIdStr in prevState) {
        const productId = Number(productIdStr);
        if (currentProductIds.has(productId)) {
          nextState[productId] = prevState[productId];
        }
      }
      return nextState;
    };
    setVariations(cleanupIds);
    setLoadingVariations(cleanupIds);
    setErrorVariations(cleanupIds);
    // Clean up initialStockValues as well? Maybe less critical.
  }, [products]); // Depend only on products prop
  // --- End Updated useEffect ---

  const handleVariationStockChange = (
    productId: number,
    variationId: number,
    newStockStr: string // Input value is string
  ) => {
    const newStock = parseInt(newStockStr, 10);
    // Handle invalid input (e.g., non-numeric, negative)
    if (isNaN(newStock) || newStock < 0) {
      // Optionally provide feedback or reset to previous value
      console.warn("Invalid stock quantity entered");
      return;
    }

    const key = `${productId}-${variationId}`;
    // Ensure initialStockValues has the key before proceeding
    const originalStock = initialStockValues[key] ?? 0; // Default to 0 if not found

    // Update local state immediately
    setVariations((prev) => {
      if (!prev[productId]) return prev; // Safety check
      return {
        ...prev,
        [productId]: prev[productId].map((variation) =>
          variation.id === variationId
            ? { ...variation, stock_quantity: newStock }
            : variation
        ),
      };
    });

    // Handle pending updates
    setPendingUpdates((prev) => {
      const filtered = prev.filter(
        (update) =>
          !(
            update.productId === productId && update.variationId === variationId
          )
      );

      // If the new stock is the same as the initial stock, remove from pending
      if (newStock === originalStock) {
        return filtered;
      }

      // Otherwise, add or update the pending update
      return [...filtered, { productId, variationId, newStock }];
    });
  };

  const saveAllChanges = async () => {
    if (pendingUpdates.length === 0) return;

    setIsLoading(true);
    try {
      // Group updates by product for potential batching if API supports it
      // For now, update one by one
      for (const update of pendingUpdates) {
        setUpdatingItemId(update.variationId); // Indicate which item is updating
        const response = await fetch(
          `/api/products/${update.productId}/variations/${update.variationId}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              stock_quantity: update.newStock,
              manage_stock: true, // Assuming stock is managed if quantity is set
            }),
          }
        );

        if (!response.ok) {
          // Try to get error message from response
          let errorMsg = `Failed to update variation ${update.variationId}`;
          try {
            const errorData = await response.json();
            errorMsg = errorData.message || errorMsg;
          } catch (e) {}
          throw new Error(errorMsg);
        }
        setUpdatingItemId(null); // Clear indicator on success
      }

      // Update initial stock values to reflect saved changes
      const newInitialValues = { ...initialStockValues };
      pendingUpdates.forEach((update) => {
        const key = `${update.productId}-${update.variationId}`;
        newInitialValues[key] = update.newStock;
      });
      setInitialStockValues(newInitialValues);

      // Clear pending updates
      setPendingUpdates([]);

      // Optionally notify parent or show success message
      // if (onProductUpdate) {
      //   await onProductUpdate(); // This might refetch all products, consider if needed
      // }
      alert("Changes saved successfully!"); // Simple feedback
    } catch (error) {
      console.error("Failed to save changes:", error);
      alert(
        `Error saving changes: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      setUpdatingItemId(null); // Clear indicator on error
    } finally {
      setIsLoading(false);
    }
  };

  const toggleProductExpansion = (productId: number) => {
    setExpandedProducts((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
    // Variation loading is now handled by the main useEffect hook watching products
  };

  // Helper to get variation attributes string
  const getVariationAttributesString = (
    variation: ProductVariation
  ): string => {
    return (
      variation.attributes?.map((attr) => attr.option).join(" - ") ||
      "Variation"
    );
  };

  // Helper to get variation SKU - FIXED
  const getVariationSku = (variation: ProductVariation): string => {
    // Find SKU within attributes array
    return (
      variation.attributes?.find((attr) => attr.name.toLowerCase() === "sku")
        ?.option || "-" // Fallback if no SKU attribute found
    );
  };

  return (
    <div className="space-y-4">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t("dashboard.table.product")}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t("dashboard.table.sku")}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t("dashboard.table.type")}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t("dashboard.table.stockStatus")}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t("dashboard.table.actions")}
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {products.map((product) => (
              <React.Fragment key={product.id}>
                <tr
                  className={
                    updatingItemId === product.id ? "bg-blue-50" : "" // Use updatingItemId
                  }
                >
                  {/* Product Name / Image */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {product.images?.[0]?.src && (
                        <img
                          src={product.images[0].src}
                          alt={product.name}
                          className="h-10 w-10 rounded-md mr-3 object-cover" // Changed to rounded-md
                        />
                      )}
                      <div className="text-sm font-medium text-gray-900">
                        {product.name}
                      </div>
                    </div>
                  </td>
                  {/* SKU */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-500">
                      {product.sku || "-"}
                    </span>
                  </td>
                  {/* Type */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900 capitalize">
                      {t(`products.type.${product.type}`)}
                    </span>
                  </td>
                  {/* Stock Status / Summary */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    {product.type === "variable" ? (
                      <div className="flex gap-1 flex-wrap">
                        {loadingVariations[product.id] ? (
                          <span className="text-xs text-gray-500">
                            Loading...
                          </span>
                        ) : errorVariations[product.id] ? (
                          <span className="text-xs text-red-500">Error</span>
                        ) : variations[product.id] ? (
                          (() => {
                            if (!Array.isArray(variations[product.id])) {
                              return (
                                <span className="text-xs text-red-500">
                                  Error
                                </span>
                              );
                            }
                            const stockSummary = getVariationStockSummary(
                              variations[product.id]
                            );
                            if (variations[product.id].length === 0) {
                              return (
                                <span className="text-xs text-gray-500">
                                  No variations
                                </span>
                              );
                            }
                            return (
                              <>
                                {stockSummary.zeroStock > 0 && (
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    {stockSummary.zeroStock}{" "}
                                    {t("products.stock.variationCount.out")}
                                  </span>
                                )}
                                {stockSummary.lowStock > 0 && (
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    {stockSummary.lowStock}{" "}
                                    {t("products.stock.variationCount.low")}
                                  </span>
                                )}
                                {stockSummary.normalStock > 0 && (
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {stockSummary.normalStock}{" "}
                                    {t("products.stock.variationCount.ok")}
                                  </span>
                                )}
                              </>
                            );
                          })()
                        ) : (
                          <span className="text-xs text-gray-500">
                            Loading...
                          </span>
                        )}
                      </div>
                    ) : (
                      <span
                        className={
                          getStockStatusInfo(product.stock_quantity, t)
                            .className
                        }
                      >
                        {getStockStatusInfo(product.stock_quantity, t).text}
                      </span>
                    )}
                  </td>
                  {/* Actions */}
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {product.type !== "variation" && (
                      <button
                        onClick={() => setEditingProduct(product)}
                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                      >
                        {t("products.actions.edit")}
                      </button>
                    )}
                    {product.type === "variable" && (
                      <button
                        onClick={() => toggleProductExpansion(product.id)}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        {expandedProducts.includes(product.id)
                          ? t("products.actions.hideVariations")
                          : t("products.actions.showVariations")}
                      </button>
                    )}
                  </td>
                </tr>
                {/* Variations rows */}
                {expandedProducts.includes(product.id) && (
                  <>
                    {loadingVariations[product.id] && (
                      <tr className="bg-gray-50">
                        <td
                          colSpan={5}
                          className="px-6 py-4 text-center text-sm text-gray-500"
                        >
                          Loading variations...
                        </td>
                      </tr>
                    )}
                    {errorVariations[product.id] &&
                      !loadingVariations[product.id] && (
                        <tr className="bg-gray-50">
                          <td
                            colSpan={5}
                            className="px-6 py-4 text-center text-sm text-red-500"
                          >
                            Error loading variations.
                          </td>
                        </tr>
                      )}
                    {!loadingVariations[product.id] &&
                      !errorVariations[product.id] &&
                      Array.isArray(variations[product.id]) &&
                      variations[product.id]?.length === 0 && (
                        <tr className="bg-gray-50">
                          <td
                            colSpan={5}
                            className="px-6 py-4 text-center text-sm text-gray-500"
                          >
                            No variations found for this product.
                          </td>
                        </tr>
                      )}
                    {!loadingVariations[product.id] &&
                      !errorVariations[product.id] &&
                      Array.isArray(variations[product.id]) &&
                      variations[product.id]?.map((variation) => {
                        const variationKey = `${product.id}-${variation.id}`;
                        const stockInfo = getStockStatusInfo(
                          variation.stock_quantity,
                          t
                        );
                        const isPending = pendingUpdates.some(
                          (upd) =>
                            upd.productId === product.id &&
                            upd.variationId === variation.id
                        );
                        return (
                          <tr
                            key={variationKey}
                            className={`bg-gray-50 ${
                              isPending ? "bg-blue-50" : ""
                            }`}
                          >
                            <td className="px-6 py-4 whitespace-nowrap pl-12">
                              <div className="text-sm text-gray-900">
                                {getVariationAttributesString(variation)}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="text-sm text-gray-500">
                                {getVariationSku(variation)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap"></td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center gap-2">
                                <input
                                  type="number"
                                  value={variation.stock_quantity ?? ""}
                                  onChange={(e) =>
                                    handleVariationStockChange(
                                      product.id,
                                      variation.id,
                                      e.target.value
                                    )
                                  }
                                  className={`w-20 px-2 py-1 text-sm border rounded focus:ring-indigo-500 focus:border-indigo-500 ${
                                    isPending
                                      ? "border-blue-300"
                                      : "border-gray-300"
                                  }`}
                                  min="0"
                                />
                                <span className={stockInfo.className}>
                                  {stockInfo.text}
                                </span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {updatingItemId === variation.id && (
                                <svg
                                  className="animate-spin h-5 w-5 text-blue-500"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                >
                                  <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                  ></circle>
                                  <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                  ></path>
                                </svg>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                  </>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {pendingUpdates.length > 0 && (
        <div className="fixed bottom-4 right-4 z-50">
          <button
            onClick={saveAllChanges}
            disabled={isLoading}
            className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <span>{t("common.saving")}</span>
              </>
            ) : (
              <>
                <span>{t("common.saveChanges")}</span>
                <span className="bg-indigo-700 px-2 py-0.5 rounded-full text-sm">
                  {pendingUpdates.length}
                </span>
              </>
            )}
          </button>
        </div>
      )}

      {editingProduct && (
        <ProductEditModal
          product={editingProduct as Product}
          isOpen={!!editingProduct}
          onClose={() => setEditingProduct(null)}
          onUpdate={onProductUpdate}
        />
      )}
    </div>
  );
}
