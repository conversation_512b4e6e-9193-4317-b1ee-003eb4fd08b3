import { NextResponse } from "next/server";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    if (
      !process.env.WC_CONSUMER_KEY ||
      !process.env.WC_CONSUMER_SECRET ||
      !process.env.NEXT_PUBLIC_WC_API_URL
    ) {
      return NextResponse.json(
        { error: "WooCommerce API credentials not configured" },
        { status: 500 }
      );
    }

    const auth = Buffer.from(
      `${process.env.WC_CONSUMER_KEY}:${process.env.WC_CONSUMER_SECRET}`
    ).toString("base64");

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WC_API_URL}/products/${params.id}/variations`,
      {
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch variations: ${response.statusText}`);
    }

    const variations = await response.json();
    return NextResponse.json(variations);
  } catch (error) {
    console.error("Variations API Error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string; variationId: string } }
) {
  try {
    const data = await request.json();
    const auth = Buffer.from(
      `${process.env.WC_CONSUMER_KEY}:${process.env.WC_CONSUMER_SECRET}`
    ).toString("base64");

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WC_API_URL}/products/${params.id}/variations/${params.variationId}`,
      {
        method: "PUT",
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to update variation: ${response.statusText}`);
    }

    return NextResponse.json(await response.json());
  } catch (error) {
    console.error("Variation Update API Error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
