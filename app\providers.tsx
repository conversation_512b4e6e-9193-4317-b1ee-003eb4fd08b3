"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState } from "react";
import { LanguageProvider } from "./context/LanguageContext";
import { SessionProvider } from "next-auth/react"; // Added import

export default function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: false,
            retry: 1,
            staleTime: 5 * 60 * 1000, // 5 minutes
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider> {/* SessionProvider wraps LanguageProvider and children */}
        <LanguageProvider>{children}</LanguageProvider>
      </SessionProvider>
    </QueryClientProvider>
  );
}
