"use client";

import { useQuery } from "@tanstack/react-query";
import { LineChart } from "./ui/charts/LineChart";
import { LoadingSpinner } from "./ui/LoadingSpinner";

interface OrderTimelineProps {
  startDate: Date;
  endDate: Date;
}

export function OrderTimeline({ startDate, endDate }: OrderTimelineProps) {
  const {
    data: orders,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["orders", startDate.toISOString(), endDate.toISOString()],
    queryFn: async () => {
      const response = await fetch(
        `/api/orders?after=${startDate.toISOString()}&before=${endDate.toISOString()}&per_page=100`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }
      return response.json();
    },
  });

  if (isLoading) return <LoadingSpinner />;
  if (error) return <div>Error loading orders</div>;
  if (!orders) return null;

  // Process orders into timeline data
  const timelineData = orders
    .sort(
      (a: any, b: any) =>
        new Date(a.date_created).getTime() - new Date(b.date_created).getTime()
    )
    .reduce((acc: any[], order: any) => {
      const date = new Date(order.date_created).toISOString().split("T")[0];
      const existingEntry = acc.find((entry) => entry.date === date);

      if (existingEntry) {
        existingEntry.value += 1;
      } else {
        acc.push({ date, value: 1 });
      }

      return acc;
    }, []);

  return (
    <LineChart
      title="Orders over time"
      currentData={timelineData}
      yAxisLabel="Count"
    />
  );
}
