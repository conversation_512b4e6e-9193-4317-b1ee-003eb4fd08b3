import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const productId = params.id;
    const data = await request.json();

    if (
      !process.env.WC_CONSUMER_KEY ||
      !process.env.WC_CONSUMER_SECRET ||
      !process.env.NEXT_PUBLIC_WC_API_URL
    ) {
      return NextResponse.json(
        { error: "WooCommerce API credentials not configured" },
        { status: 500 }
      );
    }

    const auth = Buffer.from(
      `${process.env.WC_CONSUMER_KEY}:${process.env.WC_CONSUMER_SECRET}`
    ).toString("base64");

    // Always enable stock management when updating stock
    if ("stock_quantity" in data) {
      data.manage_stock = true;
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WC_API_URL}/products/${productId}`,
      {
        method: "PUT",
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const error = await response.text();
      console.error("WooCommerce API Error:", error);
      return NextResponse.json(
        { error: `WooCommerce API error: ${response.statusText}` },
        { status: response.status }
      );
    }

    const updatedProduct = await response.json();
    return NextResponse.json(updatedProduct);
  } catch (error) {
    console.error("Product Update API Error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
