{"products": {"edit": {"title": "Edit Product"}, "fields": {"name": "Product Name", "sku": "SKU", "regularPrice": "Regular Price", "salePrice": "Sale Price", "description": "Description", "stockQuantity": "Stock Quantity", "stockStatus": "Stock Status", "manageStock": "Manage stock?"}, "actions": {"edit": "Edit", "hideVariations": "<PERSON>de", "showVariations": "Variations"}, "errors": {"updateFailed": "Failed to update product. Please try again."}, "stock": {"inStock": "In Stock", "outOfStock": "Out of Stock", "lowStock": "Low Stock", "onBackorder": "On Backorder", "variationCount": {"out": "Out", "low": "Low", "ok": "OK"}}, "type": {"simple": "Simple", "variable": "Variable", "grouped": "Grouped", "external": "External", "variation": "Variation", "gift-card": "Gift Card"}}, "common": {"save": "Save", "saving": "Saving...", "cancel": "Cancel", "saveChanges": "Save Changes"}, "filters": {"search": {"placeholder": "Search products..."}, "categories": {"all": "All Categories", "loading": "Loading categories..."}, "stockStatus": {"all": "All Stock Status", "inStock": "In Stock", "outOfStock": "Out of Stock", "onBackorder": "On Backorder"}, "reset": "Reset Filters"}, "dashboard": {"title": "Dashboard", "subtitle": "Manage your products and view statistics.", "loading": "Loading products...", "error": "Error loading products", "retry": "Retry", "noProducts": "No products found.", "refresh": "Refresh Products", "noMatchingProducts": "No products match the current filters.", "table": {"product": "Product", "sku": "SKU", "type": "Type", "stockStatus": "Stock", "actions": "Actions"}}}