interface StatisticsParams {
  start: string;
  end: string;
  page?: number;
  per_page?: number;
}

export async function fetchStatistics(params: StatisticsParams) {
  const response = await fetch("/api/statistics", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch statistics");
  }

  return response.json();
}
