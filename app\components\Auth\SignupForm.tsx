"use client";

import { useState, FormEvent, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function SignupForm() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidatingToken, setIsValidatingToken] = useState(true);
  const [invitationData, setInvitationData] = useState<any>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  const token = searchParams.get("token");

  // Validate invitation token on component mount
  useEffect(() => {
    const validateToken = async () => {
      if (!token) {
        setError(
          "No invitation token provided. You need an invitation to create an account."
        );
        setIsValidatingToken(false);
        return;
      }

      try {
        const response = await fetch("/api/invitations/validate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ token }),
        });

        const data = await response.json();

        if (response.ok && data.valid) {
          setInvitationData(data.invitation);
          setEmail(data.invitation.email); // Pre-fill email from invitation
          setError(null);
        } else {
          setError(data.message || "Invalid invitation token");
        }
      } catch (err) {
        console.error("Token validation error:", err);
        setError("Failed to validate invitation token");
      } finally {
        setIsValidatingToken(false);
      }
    };

    validateToken();
  }, [token]);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);

    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }
    if (!name || !email || !password) {
      setError("All fields are required.");
      return;
    }

    setIsLoading(true);
    console.log("Attempting signup with:", { name, email }); // Don't log password

    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email, password, token }),
      });

      const data = await response.json();
      console.log("Signup response:", data);

      if (response.ok) {
        router.push("/login?message=Signup successful! Please login.");
      } else {
        setError(data.message || "Signup failed. Please try again.");
      }
    } catch (err) {
      console.error("Signup error:", err);
      setError("An unexpected error occurred during signup.");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading while validating token
  if (isValidatingToken) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="p-8 bg-white shadow-md rounded-lg w-full max-w-md text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Validating invitation...</p>
        </div>
      </div>
    );
  }

  // Show error if token validation failed
  if (error && !invitationData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="p-8 bg-white shadow-md rounded-lg w-full max-w-md text-center">
          <h2 className="mb-4 text-2xl font-semibold text-gray-700">
            Invalid Invitation
          </h2>
          <div className="mb-4 p-3 text-sm text-red-700 bg-red-100 border border-red-400 rounded">
            {error}
          </div>
          <p className="text-gray-600 mb-4">
            Please contact an administrator for a valid invitation link.
          </p>
          <a
            href="/login"
            className="inline-block px-4 py-2 text-blue-600 border border-blue-600 rounded hover:bg-blue-50"
          >
            Back to Login
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <form
        onSubmit={handleSubmit}
        className="p-8 bg-white shadow-md rounded-lg w-full max-w-md"
      >
        <h2 className="mb-6 text-2xl font-semibold text-center text-gray-700">
          Create Account
        </h2>

        {invitationData && (
          <div className="mb-4 p-3 text-sm text-green-700 bg-green-100 border border-green-400 rounded">
            You've been invited by {invitationData.createdBy} to join as a{" "}
            <strong>{invitationData.role}</strong>.
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 text-sm text-red-700 bg-red-100 border border-red-400 rounded">
            {error}
          </div>
        )}

        <div className="mb-4">
          <label
            htmlFor="name"
            className="block mb-2 text-sm font-medium text-gray-600"
          >
            Full Name
          </label>
          <input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          />
        </div>

        <div className="mb-4">
          <label
            htmlFor="email"
            className="block mb-2 text-sm font-medium text-gray-600"
          >
            Email
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
            disabled={true}
            readOnly
          />
        </div>

        <div className="mb-4">
          <label
            htmlFor="password"
            className="block mb-2 text-sm font-medium text-gray-600"
          >
            Password
          </label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          />
        </div>

        <div className="mb-6">
          <label
            htmlFor="confirmPassword"
            className="block mb-2 text-sm font-medium text-gray-600"
          >
            Confirm Password
          </label>
          <input
            type="password"
            id="confirmPassword"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          />
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="w-full px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:opacity-50"
        >
          {isLoading ? "Creating account..." : "Sign Up"}
        </button>

        <p className="mt-4 text-sm text-center text-gray-600">
          Already have an account?{" "}
          <a
            href="/login"
            className="font-medium text-blue-600 hover:underline"
          >
            Login
          </a>
        </p>
      </form>
    </div>
  );
}
