import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../../auth/[...nextauth]/route";
import { prismaAuth } from "@/lib/db";

interface DeleteUserParams {
  params: {
    userId: string;
  };
}

export async function DELETE(req: Request, { params }: DeleteUserParams) {
  const session = await getServerSession(authOptions);

  if (!session || (session.user as any).role !== "admin") {
    return NextResponse.json(
      { message: "Forbidden: Administrator access required." },
      { status: 403 }
    );
  }

  const { userId } = params;
  if (!userId) {
    return NextResponse.json(
      { message: "User ID is required." },
      { status: 400 }
    );
  }

  try {
    // Check if user exists
    const user = await prismaAuth.user.findUnique({
      where: {
        id: userId,
      },
    });

    if (!user) {
      return NextResponse.json({ message: "User not found." }, { status: 404 });
    }

    // Delete user from database (this will cascade delete sessions and accounts due to Prisma schema)
    await prismaAuth.user.delete({
      where: {
        id: userId,
      },
    });

    return NextResponse.json(
      { message: `User ${userId} and associated data deleted successfully.` },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    return NextResponse.json(
      { message: "Error deleting user.", error: (error as Error).message },
      { status: 500 }
    );
  }
}
