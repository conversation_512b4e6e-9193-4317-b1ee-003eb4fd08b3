interface ProductProps {
  product: {
    id: number;
    name: string;
    price: string;
    images: { src: string }[];
    description: string;
  };
}

export default function ProductCard({ product }: ProductProps) {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
      <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden">
        <img
          src={product.images[0]?.src || '/placeholder.png'}
          alt={product.name}
          className="w-full h-full object-center object-cover"
        />
      </div>
      <div className="p-4">
        <h3 className="text-lg font-medium text-gray-900">{product.name}</h3>
        <p className="mt-1 text-sm text-gray-500">
          {product.description.replace(/<[^>]*>/g, '').slice(0, 100)}...
        </p>
        <div className="mt-2 flex items-center justify-between">
          <span className="text-lg font-bold text-gray-900">${product.price}</span>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            View Details
          </button>
        </div>
      </div>
    </div>
  );
}