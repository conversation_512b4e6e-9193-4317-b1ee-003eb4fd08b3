"use client";

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { StatCard } from "../components/ui/StatCard";
import { LineChart } from "../components/ui/charts/LineChart";
import { DataTable } from "../components/ui/DataTable";
import { DateRangePicker } from "../components/ui/DateRangePicker";
import { useLanguage } from "../context/LanguageContext";

export default function StatisticsPage() {
  const { t } = useLanguage();
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Default to 1 week
    end: new Date(),
  });

  const handlePeriodSelect = (period: "day" | "week" | "month") => {
    const end = new Date();
    let start = new Date();

    switch (period) {
      case "day":
        start = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case "week":
        start = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
    }

    setDateRange({ start, end });
  };

  const {
    data: stats,
    isLoading,
    error,
    isFetching,
  } = useQuery({
    queryKey: [
      "statistics",
      dateRange.start.toISOString(),
      dateRange.end.toISOString(),
    ],
    queryFn: async () => {
      const response = await fetch("/api/statistics", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          start: dateRange.start.toISOString(),
          end: dateRange.end.toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch statistics");
      }

      return response.json();
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 30 * 60 * 1000, // Keep unused data for 30 minutes
    refetchOnWindowFocus: false,
  });

  const isLoadingState = isLoading || isFetching;

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <h1 className="text-2xl font-semibold">Statistik</h1>
        <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
          <div className="flex gap-2">
            <button
              onClick={() => handlePeriodSelect("day")}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors
                ${
                  dateRange.start.getTime() ===
                  new Date(Date.now() - 24 * 60 * 60 * 1000).getTime()
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                }`}
            >
              Dag
            </button>
            <button
              onClick={() => handlePeriodSelect("week")}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors
                ${
                  dateRange.start.getTime() ===
                  new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).getTime()
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                }`}
            >
              Vecka
            </button>
            <button
              onClick={() => handlePeriodSelect("month")}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors
                ${
                  dateRange.start.getTime() ===
                  new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).getTime()
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                }`}
            >
              Månad
            </button>
          </div>
          <DateRangePicker value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* Top Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        {isLoadingState ? (
          [...Array(4)].map((_, i) => (
            <div
              key={i}
              className="bg-white p-6 rounded-lg shadow-sm border animate-pulse"
            >
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))
        ) : error ? (
          <div className="col-span-4">
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              <p>
                Error loading statistics:{" "}
                {error instanceof Error ? error.message : "Unknown error"}
              </p>
            </div>
          </div>
        ) : stats ? (
          <>
            <StatCard
              title="Total försäljning"
              value={stats.topStats.totalRevenue.value}
              trend={stats.topStats.totalRevenue.trend}
              change={stats.topStats.totalRevenue.change}
            />
            <StatCard
              title="Snitt försäljning"
              value={stats.topStats.averageOrderValue.value}
              trend={stats.topStats.averageOrderValue.trend}
              change={stats.topStats.averageOrderValue.change}
            />
            <StatCard
              title="Antal ordrar"
              value={stats.topStats.totalOrders.value}
              trend={stats.topStats.totalOrders.trend}
              change={stats.topStats.totalOrders.change}
            />
            <StatCard
              title="Antal varianter"
              value={stats.topStats.totalVariants.value}
              trend={stats.topStats.totalVariants.trend}
              change={stats.topStats.totalVariants.change}
            />
          </>
        ) : null}
      </div>

      {/* Charts */}
      {!isLoadingState && stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-white p-4 rounded-lg shadow">
            <LineChart
              title="Beställningar"
              currentData={stats.diagrams.orderTimeline.current}
              previousData={stats.diagrams.orderTimeline.previous}
              yAxisLabel="Antal"
            />
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <LineChart
              title="Försäljning"
              currentData={stats.diagrams.revenueTimeline.current}
              previousData={stats.diagrams.revenueTimeline.previous}
              yAxisLabel="SEK"
            />
          </div>
        </div>
      )}

      {/* Tables */}
      {!isLoadingState && stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <DataTable
            title="Topprodukter"
            columns={[
              {
                key: "product",
                label: "Produkt",
                format: (value, row) => `${value} ${row.variantInfo}`,
              },
              { key: "totalSales", label: "Antal sålda" },
              { key: "totalRevenue", label: "Nettoomsättning" },
            ]}
            data={stats.tables.topProducts}
          />
          <DataTable
            title="Sämst säljande produkter"
            columns={[
              {
                key: "product",
                label: "Produkt",
                format: (value, row) => `${value} ${row.variantInfo}`,
              },
              { key: "totalSales", label: "Antal sålda" },
              { key: "totalRevenue", label: "Nettoomsättning" },
            ]}
            data={stats.tables.worstProducts}
          />
        </div>
      )}
    </div>
  );
}
