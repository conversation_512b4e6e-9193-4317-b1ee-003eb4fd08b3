interface Column {
  key: string;
  label: string;
  format?: (value: any, row: Record<string, any>) => string;
}

interface DataTableProps {
  title: string;
  data: Array<Record<string, any>>;
  columns: Column[];
}

export function DataTable({ title, data, columns }: DataTableProps) {
  // Ensure data is an array, fallback to empty array if not
  const safeData = Array.isArray(data) ? data : [];

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="px-4 py-2 text-left text-sm font-semibold text-gray-600"
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {safeData.map((row, index) => (
              <tr key={index} className="border-t">
                {columns.map((column) => (
                  <td key={column.key} className="px-4 py-2 text-sm">
                    {column.format
                      ? column.format(row[column.key], row)
                      : row[column.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
