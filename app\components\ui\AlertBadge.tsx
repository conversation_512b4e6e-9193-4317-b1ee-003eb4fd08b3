interface AlertBadgeProps {
  type: "error" | "warning" | "success" | "info";
  message: string;
}

export function AlertBadge({ type, message }: AlertBadgeProps) {
  const colors = {
    error: "bg-red-100 text-red-800",
    warning: "bg-yellow-100 text-yellow-800",
    success: "bg-green-100 text-green-800",
    info: "bg-blue-100 text-blue-800",
  };

  return (
    <div className={`p-4 rounded-lg ${colors[type]}`}>
      <p>{message}</p>
    </div>
  );
}
