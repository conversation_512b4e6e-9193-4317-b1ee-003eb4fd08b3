# WooSync – WooCommerce Stock & Product Manager with User Authentication

**WooSync** is a modern web app for managing WooCommerce products using the WooCommerce REST API. The app features comprehensive user authentication, admin management, and allows inline editing of product details like stock, price, and visibility, with real-time filtering and updates—all hosted seamlessly on **Vercel**.

## 🚀 Features

### Core Features

- 🔄 **Sync with WooCommerce API**: Fetch, update, and manage product details in real time.
- ✍️ **Inline Editing**: Update product stock, price, name, or status directly in the UI.
- 🔍 **Advanced Filtering**: Filter by category, stock status, price range, visibility, and more.
- ☁️ **Hosted on Vercel**: Instant deployment, serverless functions, and lightning-fast performance.

### Authentication & User Management

- 🔐 **NextAuth.js Integration**: Secure user authentication with credentials provider
- 👥 **Admin Dashboard**: Complete user management with role-based access control
- 📧 **Invitation System**: Admin-controlled user registration via email invitations
- ⚙️ **User Preferences**: Customizable settings with immediate page updates
- 🛡️ **Role-Based Security**: Admin and user roles with protected routes

## 📦 Tech Stack

- **Frontend**: React + Tailwind CSS
- **Backend**: Node.js / Serverless API (via Vercel functions)
- **API**: WooCommerce REST API (JSON over HTTPS)
- **Deployment**: Vercel
- **State Management**: React Context / SWR or React Query for data fetching and cache

## 📁 Project Structure

```
/app
  /components         # UI components (table, filters, input cells)
  /pages              # Next.js pages (index, api handlers)
  /utils              # API client, helpers
  /styles             # Global styles (Tailwind)
.vercel               # Vercel config
.env.local            # Environment variables
README.md             # Project overview
```

## 🛠 Setup Instructions

1. **Clone the repo**

   ```bash
   git clone https://github.com/yourusername/kswoosync.git
   cd woosync
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Add environment variables**
   Create a `.env.local` file and add your WooCommerce credentials:

   ```env
   NEXT_PUBLIC_WC_API_URL=https://yourstore.com/wp-json/wc/v3
   WC_CONSUMER_KEY=your_consumer_key
   WC_CONSUMER_SECRET=your_consumer_secret
   ```

4. **Run locally**

   ```bash
   npm run dev
   ```

5. **Deploy to Vercel**
   Push to GitHub and import the project in [vercel.com](https://vercel.com/) for auto-deployment.

## ✅ TODO

- [ ] Add bulk edit capabilities
- [ ] Pagination and lazy loading
- [ ] CSV import/export
- [ ] Role-based access control

## 🧠 Notes

- Uses WooCommerce's `PUT /products/{id}` endpoint for updates.
- Products fetched via `GET /products` with query params for filtering.
- Authentication via Basic Auth (Base64 encoded key/secret) or via OAuth 1.0a (recommended for production).

## 📄 License

MIT © 2025 Your Name
