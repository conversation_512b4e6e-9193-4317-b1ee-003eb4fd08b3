import { GET } from "./route";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";

// Mock Prisma
const mockPrismaUserFindMany = jest.fn();

jest.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findMany: mockPrismaUserFindMany,
    },
  },
}));

// Mock next-auth getServerSession
const mockGetServerSession = jest.fn();
jest.mock("next-auth/next", () => ({
  getServerSession: jest.fn((...args) => mockGetServerSession(...args)),
}));

describe("GET /api/admin/users", () => {
  beforeEach(() => {
    mockGetServerSession.mockReset();
    mockPrismaUserFindMany.mockReset();
  });

  it("should return 403 if user is not authenticated", async () => {
    mockGetServerSession.mockResolvedValue(null); // No session

    const req = new Request("http://localhost/api/admin/users");
    const response = await GET(req);
    const responseBody = await response.json();

    expect(response.status).toBe(403);
    expect(responseBody.message).toBe(
      "Forbidden: Administrator access required."
    );
  });

  it("should return 403 if user is not an admin", async () => {
    mockGetServerSession.mockResolvedValue({
      user: { role: "user", id: "user1" },
    }); // Non-admin user

    const req = new Request("http://localhost/api/admin/users");
    const response = await GET(req);
    const responseBody = await response.json();

    expect(response.status).toBe(403);
    expect(responseBody.message).toBe(
      "Forbidden: Administrator access required."
    );
  });

  it("should successfully fetch users for an admin", async () => {
    mockGetServerSession.mockResolvedValue({
      user: { role: "admin", id: "admin1" },
    });

    const mockUsers = [
      {
        id: "id1",
        name: "User One",
        email: "<EMAIL>",
        role: "user",
        emailVerified: null,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "id2",
        name: "User Two",
        email: "<EMAIL>",
        role: "user",
        emailVerified: null,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "id3",
        name: "User Three",
        email: "<EMAIL>",
        role: "admin",
        emailVerified: null,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    mockPrismaUserFindMany.mockResolvedValue(mockUsers);

    const req = new Request("http://localhost/api/admin/users");
    const response = await GET(req);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody).toHaveLength(3);
    expect(responseBody[0].name).toBe("User One");
    expect(responseBody[0].hashedPassword).toBeUndefined();
    expect(responseBody[1].name).toBe("User Two");
    expect(responseBody[2].name).toBe("User Three");
    expect(responseBody[2].role).toBe("admin");

    expect(mockPrismaUserFindMany).toHaveBeenCalledWith({
      select: expect.objectContaining({
        id: true,
        name: true,
        email: true,
        role: true,
      }),
    });
  });

  it("should handle empty user list correctly", async () => {
    mockGetServerSession.mockResolvedValue({
      user: { role: "admin", id: "admin1" },
    });
    mockPrismaUserFindMany.mockResolvedValue([]); // No users found

    const req = new Request("http://localhost/api/admin/users");
    const response = await GET(req);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody).toEqual([]);
    expect(mockPrismaUserFindMany).toHaveBeenCalledTimes(1);
  });
});
