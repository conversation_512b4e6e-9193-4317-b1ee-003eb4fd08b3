"use client";

import { useState } from "react";
import NumberInput from "./NumberInput";

export default function ProductForm() {
  const [value, setValue] = useState(699);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Add API call to update product price
    console.log("Updating price to:", value);
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="flex gap-2">
        <NumberInput
          value={value}
          onChange={setValue}
          className="form-input block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        />
        <button
          type="submit"
          className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          Update Price
        </button>
      </div>
      <p className="mt-2 text-sm text-gray-500">
        Current price: ${value.toFixed(2)}
      </p>
    </form>
  );
}
